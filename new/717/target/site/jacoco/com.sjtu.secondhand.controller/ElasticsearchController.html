<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ElasticsearchController</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">secondhand</a> &gt; <a href="index.html" class="el_package">com.sjtu.secondhand.controller</a> &gt; <span class="el_class">ElasticsearchController</span></div><h1>ElasticsearchController</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">245 of 1,196</td><td class="ctr2">79%</td><td class="bar">44 of 102</td><td class="ctr2">56%</td><td class="ctr1">36</td><td class="ctr2">62</td><td class="ctr1">38</td><td class="ctr2">209</td><td class="ctr1">0</td><td class="ctr2">11</td></tr></tfoot><tbody><tr><td id="a8"><a href="ElasticsearchController.java.html#L207" class="el_method">testDirectSearch(String)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="43" height="10" title="102" alt="102"/><img src="../jacoco-resources/greenbar.gif" width="76" height="10" title="182" alt="182"/></td><td class="ctr2" id="c10">64%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="66" height="10" title="21" alt="21"/><img src="../jacoco-resources/greenbar.gif" width="53" height="10" title="17" alt="17"/></td><td class="ctr2" id="e5">44%</td><td class="ctr1" id="f0">17</td><td class="ctr2" id="g0">20</td><td class="ctr1" id="h0">14</td><td class="ctr2" id="i0">44</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a10"><a href="ElasticsearchController.java.html#L356" class="el_method">testFormattedSearch2(String)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="61" alt="61"/><img src="../jacoco-resources/greenbar.gif" width="69" height="10" title="165" alt="165"/></td><td class="ctr2" id="c8">73%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="9" alt="9"/><img src="../jacoco-resources/greenbar.gif" width="22" height="10" title="7" alt="7"/></td><td class="ctr2" id="e6">43%</td><td class="ctr1" id="f1">7</td><td class="ctr2" id="g1">9</td><td class="ctr1" id="h1">10</td><td class="ctr2" id="i1">41</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a9"><a href="ElasticsearchController.java.html#L290" class="el_method">testFormattedSearch(String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="61" alt="61"/><img src="../jacoco-resources/greenbar.gif" width="50" height="10" title="120" alt="120"/></td><td class="ctr2" id="c9">66%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="9" alt="9"/><img src="../jacoco-resources/greenbar.gif" width="22" height="10" title="7" alt="7"/></td><td class="ctr2" id="e7">43%</td><td class="ctr1" id="f2">7</td><td class="ctr2" id="g2">9</td><td class="ctr1" id="h2">10</td><td class="ctr2" id="i2">34</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a1"><a href="ElasticsearchController.java.html#L165" class="el_method">directAdvancedSearch(String, Long, Double, Double, String, String, int, int, String)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="17" alt="17"/><img src="../jacoco-resources/greenbar.gif" width="44" height="10" title="105" alt="105"/></td><td class="ctr2" id="c7">86%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="5" alt="5"/></td><td class="ctr2" id="e3">62%</td><td class="ctr1" id="f3">3</td><td class="ctr2" id="g3">5</td><td class="ctr1" id="h3">4</td><td class="ctr2" id="i3">20</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a4"><a href="ElasticsearchController.java.html#L432" class="el_method">formatSearchResponse(Map)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="42" height="10" title="100" alt="100"/></td><td class="ctr2" id="c6">96%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">50%</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g7">3</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i5">16</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a2"><a href="ElasticsearchController.java.html#L118" class="el_method">directSearch(String, int, int)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="45" height="10" title="108" alt="108"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d5"><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="8" alt="8"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g4">5</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i4">19</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a0"><a href="ElasticsearchController.java.html#L87" class="el_method">advancedSearch(String, Long, Double, Double, String, String, int, int, String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="72" alt="72"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d6"><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="6" alt="6"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g5">4</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i6">13</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a5"><a href="ElasticsearchController.java.html#L51" class="el_method">search(String, int, int)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="27" height="10" title="66" alt="66"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d7"><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="6" alt="6"/></td><td class="ctr2" id="e2">100%</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g6">4</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i7">13</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a7"><a href="ElasticsearchController.java.html#L35" class="el_method">syncAllItems()</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="23" alt="23"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i8">5</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a3"><a href="ElasticsearchController.java.html#L27" class="el_method">ElasticsearchController(ElasticsearchSyncService)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="6" alt="6"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i9">3</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a6"><a href="ElasticsearchController.java.html#L23" class="el_method">static {...}</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.5.201910111838</span></div></body></html>