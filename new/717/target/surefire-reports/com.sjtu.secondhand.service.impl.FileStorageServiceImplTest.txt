-------------------------------------------------------------------------------
Test set: com.sjtu.secondhand.service.impl.FileStorageServiceImplTest
-------------------------------------------------------------------------------
Tests run: 4, Failures: 1, Errors: 0, Skipped: 0, Time elapsed: 0.014 s <<< FAILURE! - in com.sjtu.secondhand.service.impl.FileStorageServiceImplTest
loadFileAsResource_shouldThrowException_whenFileNotExists  Time elapsed: 0.003 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <true> but was: <false>
	at com.sjtu.secondhand.service.impl.FileStorageServiceImplTest.loadFileAsResource_shouldThrowException_whenFileNotExists(FileStorageServiceImplTest.java:79)

