-------------------------------------------------------------------------------
Test set: com.sjtu.secondhand.controller.FileControllerTest
-------------------------------------------------------------------------------
Tests run: 7, Failures: 1, Errors: 2, Skipped: 0, Time elapsed: 0.048 s <<< FAILURE! - in com.sjtu.secondhand.controller.FileControllerTest
uploadMultipleImages_shouldHandleEmptyFiles  Time elapsed: 0.009 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<400>
	at com.sjtu.secondhand.controller.FileControllerTest.uploadMultipleImages_shouldHandleEmptyFiles(FileControllerTest.java:138)

downloadFile_shouldHandleFileNotFound  Time elapsed: 0.005 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: File not found
	at com.sjtu.secondhand.controller.FileControllerTest.downloadFile_shouldHandleFileNotFound(FileControllerTest.java:117)
Caused by: java.lang.RuntimeException: File not found
	at com.sjtu.secondhand.controller.FileControllerTest.downloadFile_shouldHandleFileNotFound(FileControllerTest.java:117)

uploadImage_shouldHandleStorageException  Time elapsed: 0.005 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: Storage error
	at com.sjtu.secondhand.controller.FileControllerTest.uploadImage_shouldHandleStorageException(FileControllerTest.java:104)
Caused by: java.lang.RuntimeException: Storage error
	at com.sjtu.secondhand.controller.FileControllerTest.uploadImage_shouldHandleStorageException(FileControllerTest.java:104)

