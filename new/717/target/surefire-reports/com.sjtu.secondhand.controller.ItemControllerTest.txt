-------------------------------------------------------------------------------
Test set: com.sjtu.secondhand.controller.ItemControllerTest
-------------------------------------------------------------------------------
Tests run: 36, Failures: 2, Errors: 33, Skipped: 0, Time elapsed: 0.41 s <<< FAILURE! - in com.sjtu.secondhand.controller.ItemControllerTest
deleteItem_withItemNotFound_shouldThrowException  Time elapsed: 0.024 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:116)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

createItem_shouldCreateItemSuccessfully  Time elapsed: 0.014 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:115)
  2. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:116)
  3. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:117)
  4. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:118)
  5. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:119)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

updateItemStatus_withInvalidStatus_shouldReturnError  Time elapsed: 0.008 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:116)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

deleteItem_shouldDeleteSuccessfully  Time elapsed: 0.008 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:116)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

searchWithEs_directEs_shouldReturnEsResults  Time elapsed: 0.011 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:115)
  2. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:116)
  3. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:117)
  4. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:118)
  5. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:119)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

getMyItems_shouldReturnUserItems  Time elapsed: 0.011 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:116)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

searchWithEs_fallbackToMySQL_shouldReturnMySQLResults  Time elapsed: 0.006 s  <<< ERROR!
java.lang.NullPointerException: Cannot invoke "java.lang.Integer.intValue()" because the return value of "org.mockito.Mockito.any()" is null
	at com.sjtu.secondhand.controller.ItemControllerTest.searchWithEs_fallbackToMySQL_shouldReturnMySQLResults(ItemControllerTest.java:632)

getItemComments_withNonExistentItem_shouldReturnError  Time elapsed: 0.001 s  <<< ERROR!
org.mockito.exceptions.misusing.InvalidUseOfMatchersException: 

Misplaced or misused argument matcher detected here:

-> at com.sjtu.secondhand.controller.ItemControllerTest.searchWithEs_fallbackToMySQL_shouldReturnMySQLResults(ItemControllerTest.java:632)
-> at com.sjtu.secondhand.controller.ItemControllerTest.searchWithEs_fallbackToMySQL_shouldReturnMySQLResults(ItemControllerTest.java:632)
-> at com.sjtu.secondhand.controller.ItemControllerTest.searchWithEs_fallbackToMySQL_shouldReturnMySQLResults(ItemControllerTest.java:632)
-> at com.sjtu.secondhand.controller.ItemControllerTest.searchWithEs_fallbackToMySQL_shouldReturnMySQLResults(ItemControllerTest.java:632)
-> at com.sjtu.secondhand.controller.ItemControllerTest.searchWithEs_fallbackToMySQL_shouldReturnMySQLResults(ItemControllerTest.java:632)
-> at com.sjtu.secondhand.controller.ItemControllerTest.searchWithEs_fallbackToMySQL_shouldReturnMySQLResults(ItemControllerTest.java:632)
-> at com.sjtu.secondhand.controller.ItemControllerTest.searchWithEs_fallbackToMySQL_shouldReturnMySQLResults(ItemControllerTest.java:632)

You cannot use argument matchers outside of verification or stubbing.
Examples of correct usage of argument matchers:
    when(mock.get(anyInt())).thenReturn(null);
    doThrow(new RuntimeException()).when(mock).someVoidMethod(any());
    verify(mock).someMethod(contains("foo"))

This message may appear after an NullPointerException if the last matcher is returning an object 
like any() but the stubbed method signature expect a primitive argument, in this case,
use primitive alternatives.
    when(mock.get(any())); // bad use, will raise NPE
    when(mock.get(anyInt())); // correct usage use

Also, this error might show up because you use argument matchers with methods that cannot be mocked.
Following methods *cannot* be stubbed/verified: final/private/equals()/hashCode().
Mocking methods declared on non-public parent classes is not supported.


addToFavorites_shouldAddSuccessfully  Time elapsed: 0.007 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:116)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

updateItemStatus_shouldUpdateStatusSuccessfully  Time elapsed: 0.013 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:116)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

deleteItem_withUnauthorizedUser_shouldReturnForbidden  Time elapsed: 0.013 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:116)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

updateItemStatus_withUnlistedStatus_shouldUpdateSuccessfully  Time elapsed: 0.011 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:116)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

addComment_withEmptyContent_shouldReturnError  Time elapsed: 0.008 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:115)
  2. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:116)
  3. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:117)
  4. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:118)
  5. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:119)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

updateItemStatus_withEmptyStatus_shouldReturnError  Time elapsed: 0.006 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:115)
  2. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:116)
  3. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:117)
  4. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:118)
  5. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:119)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

createItem_withEmptyCondition_shouldReturnError  Time elapsed: 0.009 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:115)
  2. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:116)
  3. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:117)
  4. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:118)
  5. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:119)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

removeFromFavorites_shouldRemoveSuccessfully  Time elapsed: 0.007 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:116)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

updateItemStatus_withItemNotFound_shouldThrowException  Time elapsed: 0.011 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:116)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

deleteItem_withNonOwner_shouldReturnForbidden  Time elapsed: 0.006 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:116)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

createItem_withEmptyName_shouldReturnError  Time elapsed: 0.019 s  <<< FAILURE!
java.lang.AssertionError: No value at JSON path "$.success"
	at com.sjtu.secondhand.controller.ItemControllerTest.createItem_withEmptyName_shouldReturnError(ItemControllerTest.java:203)
Caused by: java.lang.IllegalArgumentException: json can not be null or empty
	at com.sjtu.secondhand.controller.ItemControllerTest.createItem_withEmptyName_shouldReturnError(ItemControllerTest.java:203)

addComment_withParentId_shouldAddReplySuccessfully  Time elapsed: 0.015 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:117)
  2. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:118)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

searchWithEs_standardEs_shouldReturnResults  Time elapsed: 0.022 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "org.springframework.data.domain.Page.getContent()" because "itemPage" is null
	at com.sjtu.secondhand.controller.ItemControllerTest.searchWithEs_standardEs_shouldReturnResults(ItemControllerTest.java:615)
Caused by: java.lang.NullPointerException: Cannot invoke "org.springframework.data.domain.Page.getContent()" because "itemPage" is null
	at com.sjtu.secondhand.controller.ItemControllerTest.searchWithEs_standardEs_shouldReturnResults(ItemControllerTest.java:615)

getItemComments_shouldReturnCommentsList  Time elapsed: 0.011 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:115)
  2. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:116)
  3. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:117)
  4. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:118)
  5. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:119)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

updateItemStatus_withNullStatus_shouldReturnError  Time elapsed: 0.011 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:115)
  2. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:116)
  3. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:117)
  4. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:118)
  5. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:119)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

deleteItem_withNonExistentItem_shouldReturnError  Time elapsed: 0.009 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:116)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

getAllItemsForDebug_shouldReturnAllItems  Time elapsed: 0.029 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:115)
  2. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:116)
  3. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:117)
  4. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:118)
  5. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:119)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

searchWithEs_withException_shouldFallbackToMySQL  Time elapsed: 0.006 s  <<< ERROR!
java.lang.NullPointerException: Cannot invoke "java.lang.Integer.intValue()" because the return value of "org.mockito.Mockito.any()" is null
	at com.sjtu.secondhand.controller.ItemControllerTest.searchWithEs_withException_shouldFallbackToMySQL(ItemControllerTest.java:656)

getItems_shouldReturnItemsList  Time elapsed: 0.001 s  <<< ERROR!
org.mockito.exceptions.misusing.InvalidUseOfMatchersException: 

Misplaced or misused argument matcher detected here:

-> at com.sjtu.secondhand.controller.ItemControllerTest.searchWithEs_withException_shouldFallbackToMySQL(ItemControllerTest.java:656)
-> at com.sjtu.secondhand.controller.ItemControllerTest.searchWithEs_withException_shouldFallbackToMySQL(ItemControllerTest.java:656)
-> at com.sjtu.secondhand.controller.ItemControllerTest.searchWithEs_withException_shouldFallbackToMySQL(ItemControllerTest.java:656)
-> at com.sjtu.secondhand.controller.ItemControllerTest.searchWithEs_withException_shouldFallbackToMySQL(ItemControllerTest.java:656)
-> at com.sjtu.secondhand.controller.ItemControllerTest.searchWithEs_withException_shouldFallbackToMySQL(ItemControllerTest.java:656)
-> at com.sjtu.secondhand.controller.ItemControllerTest.searchWithEs_withException_shouldFallbackToMySQL(ItemControllerTest.java:656)
-> at com.sjtu.secondhand.controller.ItemControllerTest.searchWithEs_withException_shouldFallbackToMySQL(ItemControllerTest.java:656)

You cannot use argument matchers outside of verification or stubbing.
Examples of correct usage of argument matchers:
    when(mock.get(anyInt())).thenReturn(null);
    doThrow(new RuntimeException()).when(mock).someVoidMethod(any());
    verify(mock).someMethod(contains("foo"))

This message may appear after an NullPointerException if the last matcher is returning an object 
like any() but the stubbed method signature expect a primitive argument, in this case,
use primitive alternatives.
    when(mock.get(any())); // bad use, will raise NPE
    when(mock.get(anyInt())); // correct usage use

Also, this error might show up because you use argument matchers with methods that cannot be mocked.
Following methods *cannot* be stubbed/verified: final/private/equals()/hashCode().
Mocking methods declared on non-public parent classes is not supported.


deleteItem_shouldDeleteItemSuccessfully  Time elapsed: 0.012 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:116)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

addComment_shouldAddCommentSuccessfully  Time elapsed: 0.011 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:117)
  2. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:118)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

createItem_withNullCategoryId_shouldReturnError  Time elapsed: 0.014 s  <<< FAILURE!
java.lang.AssertionError: No value at JSON path "$.success"
	at com.sjtu.secondhand.controller.ItemControllerTest.createItem_withNullCategoryId_shouldReturnError(ItemControllerTest.java:219)
Caused by: java.lang.IllegalArgumentException: json can not be null or empty
	at com.sjtu.secondhand.controller.ItemControllerTest.createItem_withNullCategoryId_shouldReturnError(ItemControllerTest.java:219)

getItemById_shouldReturnItemDetails  Time elapsed: 0.016 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:115)
  2. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:116)
  3. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:117)
  4. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:118)
  5. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:119)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

createItem_withException_shouldReturnError  Time elapsed: 0.008 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:115)
  2. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:116)
  3. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:117)
  4. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:118)
  5. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:119)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

updateItem_shouldUpdateItemSuccessfully  Time elapsed: 0.014 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:115)
  2. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:116)
  3. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:117)
  4. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:118)
  5. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:119)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

updateItemStatus_withNonOwner_shouldReturnForbidden  Time elapsed: 0.01 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:116)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

getMyItems_withItemType_shouldReturnFilteredItems  Time elapsed: 0.007 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.controller.ItemControllerTest.setUp(ItemControllerTest.java:116)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

