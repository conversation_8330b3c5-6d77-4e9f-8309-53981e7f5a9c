-------------------------------------------------------------------------------
Test set: com.sjtu.secondhand.controller.OrderControllerTest
-------------------------------------------------------------------------------
Tests run: 25, Failures: 1, Errors: 6, Skipped: 0, Time elapsed: 0.153 s <<< FAILURE! - in com.sjtu.secondhand.controller.OrderControllerTest
confirmOrder_withException_shouldReturnError  Time elapsed: 0.007 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: Cannot confirm order
	at com.sjtu.secondhand.controller.OrderControllerTest.confirmOrder_withException_shouldReturnError(OrderControllerTest.java:223)
Caused by: java.lang.RuntimeException: Cannot confirm order
	at com.sjtu.secondhand.controller.OrderControllerTest.confirmOrder_withException_shouldReturnError(OrderControllerTest.java:223)

createOrder_withException_shouldReturnError  Time elapsed: 0.006 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: Order creation failed
	at com.sjtu.secondhand.controller.OrderControllerTest.createOrder_withException_shouldReturnError(OrderControllerTest.java:83)
Caused by: java.lang.RuntimeException: Order creation failed
	at com.sjtu.secondhand.controller.OrderControllerTest.createOrder_withException_shouldReturnError(OrderControllerTest.java:83)

cancelOrder_withException_shouldReturnError  Time elapsed: 0.007 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: Cannot cancel order
	at com.sjtu.secondhand.controller.OrderControllerTest.cancelOrder_withException_shouldReturnError(OrderControllerTest.java:253)
Caused by: java.lang.RuntimeException: Cannot cancel order
	at com.sjtu.secondhand.controller.OrderControllerTest.cancelOrder_withException_shouldReturnError(OrderControllerTest.java:253)

getOrderById_withException_shouldReturnError  Time elapsed: 0.005 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: Order not found
	at com.sjtu.secondhand.controller.OrderControllerTest.getOrderById_withException_shouldReturnError(OrderControllerTest.java:112)
Caused by: java.lang.RuntimeException: Order not found
	at com.sjtu.secondhand.controller.OrderControllerTest.getOrderById_withException_shouldReturnError(OrderControllerTest.java:112)

getMyBoughtOrders_withException_shouldReturnError  Time elapsed: 0.003 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: Service error
	at com.sjtu.secondhand.controller.OrderControllerTest.getMyBoughtOrders_withException_shouldReturnError(OrderControllerTest.java:159)
Caused by: java.lang.RuntimeException: Service error
	at com.sjtu.secondhand.controller.OrderControllerTest.getMyBoughtOrders_withException_shouldReturnError(OrderControllerTest.java:159)

acknowledgeOrder_withException_shouldReturnError  Time elapsed: 0.005 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: Cannot acknowledge order
	at com.sjtu.secondhand.controller.OrderControllerTest.acknowledgeOrder_withException_shouldReturnError(OrderControllerTest.java:283)
Caused by: java.lang.RuntimeException: Cannot acknowledge order
	at com.sjtu.secondhand.controller.OrderControllerTest.acknowledgeOrder_withException_shouldReturnError(OrderControllerTest.java:283)

acknowledgeOrder_shouldAcknowledgeOrderSuccessfully  Time elapsed: 0.008 s  <<< FAILURE!
java.lang.AssertionError: JSON path "$.message" expected:<订单确认联系成功> but was:<确认联系成功>
	at com.sjtu.secondhand.controller.OrderControllerTest.acknowledgeOrder_shouldAcknowledgeOrderSuccessfully(OrderControllerTest.java:271)

