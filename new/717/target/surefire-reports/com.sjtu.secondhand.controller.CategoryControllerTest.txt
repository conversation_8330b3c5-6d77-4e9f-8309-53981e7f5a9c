-------------------------------------------------------------------------------
Test set: com.sjtu.secondhand.controller.CategoryControllerTest
-------------------------------------------------------------------------------
Tests run: 3, Failures: 0, Errors: 1, Skipped: 0, Time elapsed: 0.018 s <<< FAILURE! - in com.sjtu.secondhand.controller.CategoryControllerTest
testGetAllCategories_ServiceThrowsException  Time elapsed: 0.006 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: Database error
	at com.sjtu.secondhand.controller.CategoryControllerTest.testGetAllCategories_ServiceThrowsException(CategoryControllerTest.java:97)
Caused by: java.lang.RuntimeException: Database error
	at com.sjtu.secondhand.controller.CategoryControllerTest.testGetAllCategories_ServiceThrowsException(CategoryControllerTest.java:97)

