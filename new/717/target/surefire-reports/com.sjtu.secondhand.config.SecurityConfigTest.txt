-------------------------------------------------------------------------------
Test set: com.sjtu.secondhand.config.SecurityConfigTest
-------------------------------------------------------------------------------
Tests run: 3, Failures: 2, Errors: 0, Skipped: 0, Time elapsed: 1.917 s <<< FAILURE! - in com.sjtu.secondhand.config.SecurityConfigTest
shouldAllowAccessToProtectedEndpointWithAuth  Time elapsed: 0.107 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<404>
	at com.sjtu.secondhand.config.SecurityConfigTest.shouldAllowAccessToProtectedEndpointWithAuth(SecurityConfigTest.java:46)

shouldAllowPublicAccessToAuthEndpoints  Time elapsed: 0.005 s  <<< FAILURE!
java.lang.AssertionError: Response status expected:<405> but was:<500>
	at com.sjtu.secondhand.config.SecurityConfigTest.shouldAllowPublicAccessToAuthEndpoints(SecurityConfigTest.java:32)

