<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" name="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.059" tests="33" errors="0" skipped="0" failures="4">
  <properties>
    <property name="socksProxyHost" value="127.0.0.1"/>
    <property name="http.proxyHost" value="127.0.0.1"/>
    <property name="java.specification.version" value="24"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Users/<USER>/Library/CloudStorage/OneDrive-Personal/SJTU/Coding/SEPP/SE25Project-15/new/717/target/test-classes:/Users/<USER>/Library/CloudStorage/OneDrive-Personal/SJTU/Coding/SEPP/SE25Project-15/new/717/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.7.15/spring-boot-starter-web-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.7.15/spring-boot-starter-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.7.15/spring-boot-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.7.15/spring-boot-autoconfigure-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.7.15/spring-boot-starter-logging-2.7.15.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.12/logback-classic-1.2.12.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.12/logback-core-1.2.12.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.17.2/log4j-to-slf4j-2.17.2.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.17.2/log4j-api-2.17.2.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.36/jul-to-slf4j-1.7.36.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.30/snakeyaml-1.30.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.7.15/spring-boot-starter-json-2.7.15.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.13.5/jackson-datatype-jdk8-2.13.5.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.13.5/jackson-module-parameter-names-2.13.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.7.15/spring-boot-starter-tomcat-2.7.15.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.79/tomcat-embed-core-9.0.79.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.79/tomcat-embed-websocket-9.0.79.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.3.29/spring-web-5.3.29.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.3.29/spring-beans-5.3.29.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.3.29/spring-webmvc-5.3.29.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.3.29/spring-context-5.3.29.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.3.29/spring-expression-5.3.29.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-webflux/2.7.15/spring-boot-starter-webflux-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-reactor-netty/2.7.15/spring-boot-starter-reactor-netty-2.7.15.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-http/1.0.35/reactor-netty-http-1.0.35.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http/4.1.97.Final/netty-codec-http-4.1.97.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http2/4.1.97.Final/netty-codec-http2-4.1.97.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns/4.1.97.Final/netty-resolver-dns-4.1.97.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-dns/4.1.97.Final/netty-codec-dns-4.1.97.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-native-macos/4.1.97.Final/netty-resolver-dns-native-macos-4.1.97.Final-osx-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-classes-macos/4.1.97.Final/netty-resolver-dns-classes-macos-4.1.97.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.97.Final/netty-transport-native-epoll-4.1.97.Final-linux-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-classes-epoll/4.1.97.Final/netty-transport-classes-epoll-4.1.97.Final.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-core/1.0.35/reactor-netty-core-1.0.35.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler-proxy/4.1.97.Final/netty-handler-proxy-4.1.97.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-socks/4.1.97.Final/netty-codec-socks-4.1.97.Final.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webflux/5.3.29/spring-webflux-5.3.29.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.4.32/reactor-core-3.4.32.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/2.7.15/spring-boot-starter-security-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.3.29/spring-aop-5.3.29.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/5.7.10/spring-security-config-5.7.10.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/5.7.10/spring-security-web-5.7.10.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/2.7.15/spring-boot-starter-data-jpa-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/2.7.15/spring-boot-starter-aop-2.7.15.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.7/aspectjweaver-1.9.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.7.15/spring-boot-starter-jdbc-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.3.29/spring-jdbc-5.3.29.jar:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/1.3.3/jakarta.transaction-api-1.3.3.jar:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/2.2.3/jakarta.persistence-api-2.2.3.jar:/Users/<USER>/.m2/repository/org/hibernate/hibernate-core/5.6.15.Final/hibernate-core-5.6.15.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.3.Final/jboss-logging-3.4.3.Final.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.12.23/byte-buddy-1.12.23.jar:/Users/<USER>/.m2/repository/antlr/antlr/2.7.7/antlr-2.7.7.jar:/Users/<USER>/.m2/repository/org/jboss/jandex/2.4.2.Final/jandex-2.4.2.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/Users/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/5.1.2.Final/hibernate-commons-annotations-5.1.2.Final.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/2.7.15/spring-data-jpa-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.7.15/spring-data-commons-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/spring-orm/5.3.29/spring-orm-5.3.29.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.3.29/spring-tx-5.3.29.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aspects/5.3.29/spring-aspects-5.3.29.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.7.15/spring-boot-starter-validation-2.7.15.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.79/tomcat-embed-el-9.0.79.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.2.5.Final/hibernate-validator-6.2.5.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt/0.9.1/jjwt-0.9.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.5/jackson-databind-2.13.5.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-ui/1.6.15/springdoc-openapi-ui-1.6.15.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-webmvc-core/1.6.15/springdoc-openapi-webmvc-core-1.6.15.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-common/1.6.15/springdoc-openapi-common-1.6.15.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core/2.2.8/swagger-core-2.2.8.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.13.5/jackson-dataformat-yaml-2.13.5.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations/2.2.8/swagger-annotations-2.2.8.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models/2.2.8/swagger-models-2.2.8.jar:/Users/<USER>/.m2/repository/org/webjars/swagger-ui/4.17.1/swagger-ui-4.17.1.jar:/Users/<USER>/.m2/repository/org/webjars/webjars-locator-core/0.50/webjars-locator-core-0.50.jar:/Users/<USER>/.m2/repository/io/github/classgraph/classgraph/4.8.149/classgraph-4.8.149.jar:/Users/<USER>/.m2/repository/com/h2database/h2/2.1.214/h2-2.1.214.jar:/Users/<USER>/.m2/repository/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/4.0.3/HikariCP-4.0.3.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/2.7.15/spring-boot-starter-data-redis-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.7.15/spring-data-redis-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.7.15/spring-data-keyvalue-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.3.29/spring-oxm-5.3.29.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.3.29/spring-context-support-5.3.29.jar:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/6.1.10.RELEASE/lettuce-core-6.1.10.RELEASE.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.97.Final/netty-common-4.1.97.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.97.Final/netty-handler-4.1.97.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.97.Final/netty-resolver-4.1.97.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.97.Final/netty-buffer-4.1.97.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.97.Final/netty-transport-native-unix-common-4.1.97.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.97.Final/netty-codec-4.1.97.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.97.Final/netty-transport-4.1.97.Final.jar:/Users/<USER>/.m2/repository/com/aliyun/oss/aliyun-sdk-oss/3.15.1/aliyun-sdk-oss-3.15.1.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.14/httpclient-4.5.14.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/Users/<USER>/.m2/repository/org/jdom/jdom2/2.0.6.1/jdom2-2.0.6.1.jar:/Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-core/4.5.10/aliyun-java-sdk-core-4.5.10.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.9.1/gson-2.9.1.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/org/jacoco/org.jacoco.agent/0.8.5/org.jacoco.agent-0.8.5-runtime.jar:/Users/<USER>/.m2/repository/org/ini4j/ini4j/0.5.4/ini4j-0.5.4.jar:/Users/<USER>/.m2/repository/io/opentracing/opentracing-api/0.33.0/opentracing-api-0.33.0.jar:/Users/<USER>/.m2/repository/io/opentracing/opentracing-util/0.33.0/opentracing-util-0.33.0.jar:/Users/<USER>/.m2/repository/io/opentracing/opentracing-noop/0.33.0/opentracing-noop-0.33.0.jar:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.1.0/aliyun-java-sdk-ram-3.1.0.jar:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-kms/2.11.0/aliyun-java-sdk-kms-2.11.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-elasticsearch/2.7.15/spring-boot-starter-data-elasticsearch-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-elasticsearch/4.4.15/spring-data-elasticsearch-4.4.15.jar:/Users/<USER>/.m2/repository/org/elasticsearch/client/elasticsearch-rest-high-level-client/7.17.12/elasticsearch-rest-high-level-client-7.17.12.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch/7.17.12/elasticsearch-7.17.12.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-core/7.17.12/elasticsearch-core-7.17.12.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-secure-sm/7.17.12/elasticsearch-secure-sm-7.17.12.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-x-content/7.17.12/elasticsearch-x-content-7.17.12.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-smile/2.13.5/jackson-dataformat-smile-2.13.5.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-cbor/2.13.5/jackson-dataformat-cbor-2.13.5.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-geo/7.17.12/elasticsearch-geo-7.17.12.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-lz4/7.17.12/elasticsearch-lz4-7.17.12.jar:/Users/<USER>/.m2/repository/org/lz4/lz4-java/1.8.0/lz4-java-1.8.0.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-core/8.11.1/lucene-core-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-analyzers-common/8.11.1/lucene-analyzers-common-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-backward-codecs/8.11.1/lucene-backward-codecs-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-grouping/8.11.1/lucene-grouping-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-highlighter/8.11.1/lucene-highlighter-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-join/8.11.1/lucene-join-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-memory/8.11.1/lucene-memory-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-misc/8.11.1/lucene-misc-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-queries/8.11.1/lucene-queries-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-queryparser/8.11.1/lucene-queryparser-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-sandbox/8.11.1/lucene-sandbox-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-spatial3d/8.11.1/lucene-spatial3d-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-suggest/8.11.1/lucene-suggest-8.11.1.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-cli/7.17.12/elasticsearch-cli-7.17.12.jar:/Users/<USER>/.m2/repository/net/sf/jopt-simple/jopt-simple/5.0.2/jopt-simple-5.0.2.jar:/Users/<USER>/.m2/repository/com/carrotsearch/hppc/0.8.1/hppc-0.8.1.jar:/Users/<USER>/.m2/repository/joda-time/joda-time/2.10.10/joda-time-2.10.10.jar:/Users/<USER>/.m2/repository/com/tdunning/t-digest/3.2/t-digest-3.2.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.9/HdrHistogram-2.1.9.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.10.0/jna-5.10.0.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-plugin-classloader/7.17.12/elasticsearch-plugin-classloader-7.17.12.jar:/Users/<USER>/.m2/repository/org/elasticsearch/plugin/mapper-extras-client/7.17.12/mapper-extras-client-7.17.12.jar:/Users/<USER>/.m2/repository/org/elasticsearch/plugin/parent-join-client/7.17.12/parent-join-client-7.17.12.jar:/Users/<USER>/.m2/repository/org/elasticsearch/plugin/aggs-matrix-stats-client/7.17.12/aggs-matrix-stats-client-7.17.12.jar:/Users/<USER>/.m2/repository/org/elasticsearch/plugin/rank-eval-client/7.17.12/rank-eval-client-7.17.12.jar:/Users/<USER>/.m2/repository/org/elasticsearch/plugin/lang-mustache-client/7.17.12/lang-mustache-client-7.17.12.jar:/Users/<USER>/.m2/repository/com/github/spullara/mustache/java/compiler/0.9.6/compiler-0.9.6.jar:/Users/<USER>/.m2/repository/co/elastic/clients/elasticsearch-java/7.17.12/elasticsearch-java-7.17.12.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/jakarta/json/jakarta.json-api/1.1.6/jakarta.json-api-1.1.6.jar:/Users/<USER>/.m2/repository/org/eclipse/parsson/parsson/1.0.0/parsson-1.0.0.jar:/Users/<USER>/.m2/repository/org/elasticsearch/client/elasticsearch-rest-client/7.17.12/elasticsearch-rest-client-7.17.12.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpasyncclient/4.1.5/httpasyncclient-4.1.5.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore-nio/4.4.16/httpcore-nio-4.4.16.jar:/Users/<USER>/.m2/repository/org/glassfish/jakarta.json/2.0.1/jakarta.json-2.0.1.jar:/Users/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.3.1/jaxb-api-2.3.1.jar:/Users/<USER>/.m2/repository/javax/activation/javax.activation-api/1.2.0/javax.activation-api-1.2.0.jar:/Users/<USER>/.m2/repository/javax/activation/activation/1.1.1/activation-1.1.1.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/2.3.3/jaxb-runtime-2.3.3.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/2.3.8/txw2-2.3.8.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/3.0.11/istack-commons-runtime-3.0.11.jar:/Users/<USER>/.m2/repository/com/sun/activation/jakarta.activation/1.2.2/jakarta.activation-1.2.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.13.5/jackson-datatype-jsr310-2.13.5.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.5/jackson-annotations-2.13.5.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.5/jackson-core-2.13.5.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.28/lombok-1.18.28.jar:/Users/<USER>/.m2/repository/org/flywaydb/flyway-core/8.5.13/flyway-core-8.5.13.jar:/Users/<USER>/.m2/repository/org/flywaydb/flyway-mysql/8.5.13/flyway-mysql-8.5.13.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/2.7.15/spring-boot-starter-test-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/2.7.15/spring-boot-test-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/2.7.15/spring-boot-test-autoconfigure-2.7.15.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.7.0/json-path-2.7.0.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.4.11/json-smart-2.4.11.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.4.11/accessors-smart-2.4.11.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/9.3/asm-9.3.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.22.0/assertj-core-3.22.0.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.8.2/junit-jupiter-5.8.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.8.2/junit-jupiter-api-5.8.2.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.2.0/opentest4j-1.2.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.8.2/junit-platform-commons-1.8.2.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.8.2/junit-jupiter-params-5.8.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.8.2/junit-jupiter-engine-5.8.2.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.8.2/junit-platform-engine-1.8.2.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/4.5.1/mockito-core-4.5.1.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.12.23/byte-buddy-agent-1.12.23.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/4.5.1/mockito-junit-jupiter-4.5.1.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.1/jsonassert-1.5.1.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.3.29/spring-core-5.3.29.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.3.29/spring-jcl-5.3.29.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/5.3.29/spring-test-5.3.29.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.9.1/xmlunit-core-2.9.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-test/5.7.10/spring-security-test-5.7.10.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/5.7.10/spring-security-core-5.7.10.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/5.7.10/spring-security-crypto-5.7.10.jar:"/>
    <property name="https.proxyPort" value="7897"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="24"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="US"/>
    <property name="sun.boot.library.path" value="/Users/<USER>/Library/Java/JavaVirtualMachines/openjdk-24/Contents/Home/lib"/>
    <property name="sun.java.command" value="/Users/<USER>/Library/CloudStorage/OneDrive-Personal/SJTU/Coding/SEPP/SE25Project-15/new/717/target/surefire/surefirebooter15763600252471954802.jar /Users/<USER>/Library/CloudStorage/OneDrive-Personal/SJTU/Coding/SEPP/SE25Project-15/new/717/target/surefire 2025-07-18T12-34-49_847-jvmRun1 surefire14399629189950264111tmp surefire_02573139325779148093tmp"/>
    <property name="http.nonProxyHosts" value="127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|&lt;local&gt;|*.&lt;local&gt;"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="/Users/<USER>/Library/CloudStorage/OneDrive-Personal/SJTU/Coding/SEPP/SE25Project-15/new/717/target/test-classes:/Users/<USER>/Library/CloudStorage/OneDrive-Personal/SJTU/Coding/SEPP/SE25Project-15/new/717/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.7.15/spring-boot-starter-web-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.7.15/spring-boot-starter-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.7.15/spring-boot-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.7.15/spring-boot-autoconfigure-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.7.15/spring-boot-starter-logging-2.7.15.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.12/logback-classic-1.2.12.jar:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.12/logback-core-1.2.12.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.17.2/log4j-to-slf4j-2.17.2.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.17.2/log4j-api-2.17.2.jar:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.36/jul-to-slf4j-1.7.36.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.30/snakeyaml-1.30.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.7.15/spring-boot-starter-json-2.7.15.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.13.5/jackson-datatype-jdk8-2.13.5.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.13.5/jackson-module-parameter-names-2.13.5.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.7.15/spring-boot-starter-tomcat-2.7.15.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.79/tomcat-embed-core-9.0.79.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.79/tomcat-embed-websocket-9.0.79.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.3.29/spring-web-5.3.29.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.3.29/spring-beans-5.3.29.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.3.29/spring-webmvc-5.3.29.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.3.29/spring-context-5.3.29.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.3.29/spring-expression-5.3.29.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-webflux/2.7.15/spring-boot-starter-webflux-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-reactor-netty/2.7.15/spring-boot-starter-reactor-netty-2.7.15.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-http/1.0.35/reactor-netty-http-1.0.35.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http/4.1.97.Final/netty-codec-http-4.1.97.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http2/4.1.97.Final/netty-codec-http2-4.1.97.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns/4.1.97.Final/netty-resolver-dns-4.1.97.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-dns/4.1.97.Final/netty-codec-dns-4.1.97.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-native-macos/4.1.97.Final/netty-resolver-dns-native-macos-4.1.97.Final-osx-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver-dns-classes-macos/4.1.97.Final/netty-resolver-dns-classes-macos-4.1.97.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-epoll/4.1.97.Final/netty-transport-native-epoll-4.1.97.Final-linux-x86_64.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-classes-epoll/4.1.97.Final/netty-transport-classes-epoll-4.1.97.Final.jar:/Users/<USER>/.m2/repository/io/projectreactor/netty/reactor-netty-core/1.0.35/reactor-netty-core-1.0.35.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler-proxy/4.1.97.Final/netty-handler-proxy-4.1.97.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-socks/4.1.97.Final/netty-codec-socks-4.1.97.Final.jar:/Users/<USER>/.m2/repository/org/springframework/spring-webflux/5.3.29/spring-webflux-5.3.29.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.4.32/reactor-core-3.4.32.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/2.7.15/spring-boot-starter-security-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.3.29/spring-aop-5.3.29.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/5.7.10/spring-security-config-5.7.10.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/5.7.10/spring-security-web-5.7.10.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/2.7.15/spring-boot-starter-data-jpa-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/2.7.15/spring-boot-starter-aop-2.7.15.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.7/aspectjweaver-1.9.7.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.7.15/spring-boot-starter-jdbc-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.3.29/spring-jdbc-5.3.29.jar:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/1.3.3/jakarta.transaction-api-1.3.3.jar:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/2.2.3/jakarta.persistence-api-2.2.3.jar:/Users/<USER>/.m2/repository/org/hibernate/hibernate-core/5.6.15.Final/hibernate-core-5.6.15.Final.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.3.Final/jboss-logging-3.4.3.Final.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.12.23/byte-buddy-1.12.23.jar:/Users/<USER>/.m2/repository/antlr/antlr/2.7.7/antlr-2.7.7.jar:/Users/<USER>/.m2/repository/org/jboss/jandex/2.4.2.Final/jandex-2.4.2.Final.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/Users/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/5.1.2.Final/hibernate-commons-annotations-5.1.2.Final.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/2.7.15/spring-data-jpa-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.7.15/spring-data-commons-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/spring-orm/5.3.29/spring-orm-5.3.29.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.3.29/spring-tx-5.3.29.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aspects/5.3.29/spring-aspects-5.3.29.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.7.15/spring-boot-starter-validation-2.7.15.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.79/tomcat-embed-el-9.0.79.jar:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.2.5.Final/hibernate-validator-6.2.5.Final.jar:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt/0.9.1/jjwt-0.9.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.5/jackson-databind-2.13.5.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-ui/1.6.15/springdoc-openapi-ui-1.6.15.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-webmvc-core/1.6.15/springdoc-openapi-webmvc-core-1.6.15.jar:/Users/<USER>/.m2/repository/org/springdoc/springdoc-openapi-common/1.6.15/springdoc-openapi-common-1.6.15.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-core/2.2.8/swagger-core-2.2.8.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.13.5/jackson-dataformat-yaml-2.13.5.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-annotations/2.2.8/swagger-annotations-2.2.8.jar:/Users/<USER>/.m2/repository/io/swagger/core/v3/swagger-models/2.2.8/swagger-models-2.2.8.jar:/Users/<USER>/.m2/repository/org/webjars/swagger-ui/4.17.1/swagger-ui-4.17.1.jar:/Users/<USER>/.m2/repository/org/webjars/webjars-locator-core/0.50/webjars-locator-core-0.50.jar:/Users/<USER>/.m2/repository/io/github/classgraph/classgraph/4.8.149/classgraph-4.8.149.jar:/Users/<USER>/.m2/repository/com/h2database/h2/2.1.214/h2-2.1.214.jar:/Users/<USER>/.m2/repository/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/4.0.3/HikariCP-4.0.3.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/2.7.15/spring-boot-starter-data-redis-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.7.15/spring-data-redis-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.7.15/spring-data-keyvalue-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.3.29/spring-oxm-5.3.29.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.3.29/spring-context-support-5.3.29.jar:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/6.1.10.RELEASE/lettuce-core-6.1.10.RELEASE.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.97.Final/netty-common-4.1.97.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.97.Final/netty-handler-4.1.97.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.97.Final/netty-resolver-4.1.97.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.97.Final/netty-buffer-4.1.97.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.97.Final/netty-transport-native-unix-common-4.1.97.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.97.Final/netty-codec-4.1.97.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.97.Final/netty-transport-4.1.97.Final.jar:/Users/<USER>/.m2/repository/com/aliyun/oss/aliyun-sdk-oss/3.15.1/aliyun-sdk-oss-3.15.1.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.14/httpclient-4.5.14.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/Users/<USER>/.m2/repository/org/jdom/jdom2/2.0.6.1/jdom2-2.0.6.1.jar:/Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-core/4.5.10/aliyun-java-sdk-core-4.5.10.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.9.1/gson-2.9.1.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/Users/<USER>/.m2/repository/org/jacoco/org.jacoco.agent/0.8.5/org.jacoco.agent-0.8.5-runtime.jar:/Users/<USER>/.m2/repository/org/ini4j/ini4j/0.5.4/ini4j-0.5.4.jar:/Users/<USER>/.m2/repository/io/opentracing/opentracing-api/0.33.0/opentracing-api-0.33.0.jar:/Users/<USER>/.m2/repository/io/opentracing/opentracing-util/0.33.0/opentracing-util-0.33.0.jar:/Users/<USER>/.m2/repository/io/opentracing/opentracing-noop/0.33.0/opentracing-noop-0.33.0.jar:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.1.0/aliyun-java-sdk-ram-3.1.0.jar:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-kms/2.11.0/aliyun-java-sdk-kms-2.11.0.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-elasticsearch/2.7.15/spring-boot-starter-data-elasticsearch-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-elasticsearch/4.4.15/spring-data-elasticsearch-4.4.15.jar:/Users/<USER>/.m2/repository/org/elasticsearch/client/elasticsearch-rest-high-level-client/7.17.12/elasticsearch-rest-high-level-client-7.17.12.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch/7.17.12/elasticsearch-7.17.12.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-core/7.17.12/elasticsearch-core-7.17.12.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-secure-sm/7.17.12/elasticsearch-secure-sm-7.17.12.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-x-content/7.17.12/elasticsearch-x-content-7.17.12.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-smile/2.13.5/jackson-dataformat-smile-2.13.5.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-cbor/2.13.5/jackson-dataformat-cbor-2.13.5.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-geo/7.17.12/elasticsearch-geo-7.17.12.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-lz4/7.17.12/elasticsearch-lz4-7.17.12.jar:/Users/<USER>/.m2/repository/org/lz4/lz4-java/1.8.0/lz4-java-1.8.0.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-core/8.11.1/lucene-core-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-analyzers-common/8.11.1/lucene-analyzers-common-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-backward-codecs/8.11.1/lucene-backward-codecs-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-grouping/8.11.1/lucene-grouping-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-highlighter/8.11.1/lucene-highlighter-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-join/8.11.1/lucene-join-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-memory/8.11.1/lucene-memory-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-misc/8.11.1/lucene-misc-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-queries/8.11.1/lucene-queries-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-queryparser/8.11.1/lucene-queryparser-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-sandbox/8.11.1/lucene-sandbox-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-spatial3d/8.11.1/lucene-spatial3d-8.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-suggest/8.11.1/lucene-suggest-8.11.1.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-cli/7.17.12/elasticsearch-cli-7.17.12.jar:/Users/<USER>/.m2/repository/net/sf/jopt-simple/jopt-simple/5.0.2/jopt-simple-5.0.2.jar:/Users/<USER>/.m2/repository/com/carrotsearch/hppc/0.8.1/hppc-0.8.1.jar:/Users/<USER>/.m2/repository/joda-time/joda-time/2.10.10/joda-time-2.10.10.jar:/Users/<USER>/.m2/repository/com/tdunning/t-digest/3.2/t-digest-3.2.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.9/HdrHistogram-2.1.9.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.10.0/jna-5.10.0.jar:/Users/<USER>/.m2/repository/org/elasticsearch/elasticsearch-plugin-classloader/7.17.12/elasticsearch-plugin-classloader-7.17.12.jar:/Users/<USER>/.m2/repository/org/elasticsearch/plugin/mapper-extras-client/7.17.12/mapper-extras-client-7.17.12.jar:/Users/<USER>/.m2/repository/org/elasticsearch/plugin/parent-join-client/7.17.12/parent-join-client-7.17.12.jar:/Users/<USER>/.m2/repository/org/elasticsearch/plugin/aggs-matrix-stats-client/7.17.12/aggs-matrix-stats-client-7.17.12.jar:/Users/<USER>/.m2/repository/org/elasticsearch/plugin/rank-eval-client/7.17.12/rank-eval-client-7.17.12.jar:/Users/<USER>/.m2/repository/org/elasticsearch/plugin/lang-mustache-client/7.17.12/lang-mustache-client-7.17.12.jar:/Users/<USER>/.m2/repository/com/github/spullara/mustache/java/compiler/0.9.6/compiler-0.9.6.jar:/Users/<USER>/.m2/repository/co/elastic/clients/elasticsearch-java/7.17.12/elasticsearch-java-7.17.12.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/jakarta/json/jakarta.json-api/1.1.6/jakarta.json-api-1.1.6.jar:/Users/<USER>/.m2/repository/org/eclipse/parsson/parsson/1.0.0/parsson-1.0.0.jar:/Users/<USER>/.m2/repository/org/elasticsearch/client/elasticsearch-rest-client/7.17.12/elasticsearch-rest-client-7.17.12.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpasyncclient/4.1.5/httpasyncclient-4.1.5.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore-nio/4.4.16/httpcore-nio-4.4.16.jar:/Users/<USER>/.m2/repository/org/glassfish/jakarta.json/2.0.1/jakarta.json-2.0.1.jar:/Users/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.3.1/jaxb-api-2.3.1.jar:/Users/<USER>/.m2/repository/javax/activation/javax.activation-api/1.2.0/javax.activation-api-1.2.0.jar:/Users/<USER>/.m2/repository/javax/activation/activation/1.1.1/activation-1.1.1.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/2.3.3/jaxb-runtime-2.3.3.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.3/jakarta.xml.bind-api-2.3.3.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/2.3.8/txw2-2.3.8.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/3.0.11/istack-commons-runtime-3.0.11.jar:/Users/<USER>/.m2/repository/com/sun/activation/jakarta.activation/1.2.2/jakarta.activation-1.2.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.13.5/jackson-datatype-jsr310-2.13.5.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.5/jackson-annotations-2.13.5.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.5/jackson-core-2.13.5.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.28/lombok-1.18.28.jar:/Users/<USER>/.m2/repository/org/flywaydb/flyway-core/8.5.13/flyway-core-8.5.13.jar:/Users/<USER>/.m2/repository/org/flywaydb/flyway-mysql/8.5.13/flyway-mysql-8.5.13.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/2.7.15/spring-boot-starter-test-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/2.7.15/spring-boot-test-2.7.15.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/2.7.15/spring-boot-test-autoconfigure-2.7.15.jar:/Users/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.7.0/json-path-2.7.0.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.4.11/json-smart-2.4.11.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/2.4.11/accessors-smart-2.4.11.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/9.3/asm-9.3.jar:/Users/<USER>/.m2/repository/org/assertj/assertj-core/3.22.0/assertj-core-3.22.0.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.8.2/junit-jupiter-5.8.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.8.2/junit-jupiter-api-5.8.2.jar:/Users/<USER>/.m2/repository/org/opentest4j/opentest4j/1.2.0/opentest4j-1.2.0.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.8.2/junit-platform-commons-1.8.2.jar:/Users/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.8.2/junit-jupiter-params-5.8.2.jar:/Users/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.8.2/junit-jupiter-engine-5.8.2.jar:/Users/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.8.2/junit-platform-engine-1.8.2.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/4.5.1/mockito-core-4.5.1.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.12.23/byte-buddy-agent-1.12.23.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/4.5.1/mockito-junit-jupiter-4.5.1.jar:/Users/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.1/jsonassert-1.5.1.jar:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.3.29/spring-core-5.3.29.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.3.29/spring-jcl-5.3.29.jar:/Users/<USER>/.m2/repository/org/springframework/spring-test/5.3.29/spring-test-5.3.29.jar:/Users/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.9.1/xmlunit-core-2.9.1.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-test/5.7.10/spring-security-test-5.7.10.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/5.7.10/spring-security-core-5.7.10.jar:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/5.7.10/spring-security-crypto-5.7.10.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="zh"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-03-18"/>
    <property name="java.home" value="/Users/<USER>/Library/Java/JavaVirtualMachines/openjdk-24/Contents/Home"/>
    <property name="file.separator" value="/"/>
    <property name="https.proxyHost" value="127.0.0.1"/>
    <property name="basedir" value="/Users/<USER>/Library/CloudStorage/OneDrive-Personal/SJTU/Coding/SEPP/SE25Project-15/new/717"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="apple.awt.application.name" value="ForkedBooter"/>
    <property name="surefire.real.class.path" value="/Users/<USER>/Library/CloudStorage/OneDrive-Personal/SJTU/Coding/SEPP/SE25Project-15/new/717/target/surefire/surefirebooter15763600252471954802.jar"/>
    <property name="user.script" value="Hans"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|&lt;local&gt;|*.&lt;local&gt;"/>
    <property name="java.runtime.version" value="24+36-3646"/>
    <property name="user.name" value="edwin"/>
    <property name="stdout.encoding" value="UTF-8"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="26.0"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="/var/folders/wb/c721kc454hjg9sg84x1870r80000gp/T/"/>
    <property name="java.version" value="24"/>
    <property name="user.dir" value="/Users/<USER>/Library/CloudStorage/OneDrive-Personal/SJTU/Coding/SEPP/SE25Project-15/new/717"/>
    <property name="os.arch" value="aarch64"/>
    <property name="socksProxyPort" value="7897"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="UTF-8"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="24+36-3646"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="socksNonProxyHosts" value="127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|&lt;local&gt;|*.&lt;local&gt;"/>
    <property name="java.class.version" value="68.0"/>
    <property name="http.proxyPort" value="7897"/>
  </properties>
  <testcase name="orderShouldHaveCorrectInitialStatus" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0"/>
  <testcase name="createOrder_shouldFailWhenItemTypeIsWanted" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0">
    <failure message="Unexpected exception type thrown ==&gt; expected: &lt;com.sjtu.secondhand.exception.ApiException&gt; but was: &lt;java.lang.NullPointerException&gt;" type="org.opentest4j.AssertionFailedError"><![CDATA[org.opentest4j.AssertionFailedError: Unexpected exception type thrown ==> expected: <com.sjtu.secondhand.exception.ApiException> but was: <java.lang.NullPointerException>
	at com.sjtu.secondhand.service.impl.OrderServiceImplTest.createOrder_shouldFailWhenItemTypeIsWanted(OrderServiceImplTest.java:315)
Caused by: java.lang.NullPointerException: Cannot invoke "com.sjtu.secondhand.model.Order.getId()" because "order" is null
	at com.sjtu.secondhand.service.impl.OrderServiceImplTest.lambda$createOrder_shouldFailWhenItemTypeIsWanted$0(OrderServiceImplTest.java:315)
	at com.sjtu.secondhand.service.impl.OrderServiceImplTest.createOrder_shouldFailWhenItemTypeIsWanted(OrderServiceImplTest.java:315)
]]></failure>
  </testcase>
  <testcase name="confirmOrder_shouldThrowException_whenNotSeller" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.002"/>
  <testcase name="getOrderById_shouldThrowException_whenUserNotAuthorized" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.001"/>
  <testcase name="confirmOrder_shouldSendNotificationEvent" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.003"/>
  <testcase name="cancelOrder_shouldSucceed_whenSellerCancels" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.002"/>
  <testcase name="getMyBoughtOrders_shouldReturnEmptyPage_whenNoOrders" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.002"/>
  <testcase name="createOrder_shouldFail_whenItemIsNotForSale" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.001"/>
  <testcase name="createOrder_shouldFail_whenBuyerIsSeller" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.003"/>
  <testcase name="confirmContact_shouldSucceed_whenBuyerConfirms" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.003"/>
  <testcase name="createOrder_shouldFail_whenItemIsAlreadyReserved" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.001"/>
  <testcase name="cancelOrder_shouldFail_whenUserNotParticipant" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0"/>
  <testcase name="createOrder_shouldValidateItemOwnership" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.001"/>
  <testcase name="createOrder_shouldFail_whenItemNotFound" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0"/>
  <testcase name="getMyBoughtOrders_shouldReturnPagedResults" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.001"/>
  <testcase name="completeOrder_shouldHandleNotificationEventException" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.002"/>
  <testcase name="getOrderById_shouldReturnOrder_whenOrderExists" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.001">
    <failure message="expected: com.sjtu.secondhand.model.Order$OrderStatus@62d85d39&lt;PENDING_CONFIRMATION&gt; but was: java.lang.String@2ebd0d23&lt;PENDING_CONFIRMATION&gt;" type="org.opentest4j.AssertionFailedError"><![CDATA[org.opentest4j.AssertionFailedError: expected: com.sjtu.secondhand.model.Order$OrderStatus@62d85d39<PENDING_CONFIRMATION> but was: java.lang.String@2ebd0d23<PENDING_CONFIRMATION>
	at com.sjtu.secondhand.service.impl.OrderServiceImplTest.getOrderById_shouldReturnOrder_whenOrderExists(OrderServiceImplTest.java:171)
]]></failure>
  </testcase>
  <testcase name="completeOrder_shouldFail_whenNotBuyer" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.002"/>
  <testcase name="getOrderById_shouldHandleValidOrderAccess" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.002">
    <failure message="expected: com.sjtu.secondhand.model.Order$OrderStatus@62d85d39&lt;PENDING_CONFIRMATION&gt; but was: java.lang.String@2ebd0d23&lt;PENDING_CONFIRMATION&gt;" type="org.opentest4j.AssertionFailedError"><![CDATA[org.opentest4j.AssertionFailedError: expected: com.sjtu.secondhand.model.Order$OrderStatus@62d85d39<PENDING_CONFIRMATION> but was: java.lang.String@2ebd0d23<PENDING_CONFIRMATION>
	at com.sjtu.secondhand.service.impl.OrderServiceImplTest.getOrderById_shouldHandleValidOrderAccess(OrderServiceImplTest.java:229)
]]></failure>
  </testcase>
  <testcase name="cancelOrder_shouldSucceed_whenBuyerCancels" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.003"/>
  <testcase name="createOrder_shouldHandleNotificationEventException" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.003"/>
  <testcase name="getMySoldOrders_shouldReturnPagedResults" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.003"/>
  <testcase name="confirmContact_shouldFail_whenNotBuyer" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.004"/>
  <testcase name="getOrderById_shouldThrowException_whenOrderNotFound" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.002"/>
  <testcase name="cancelOrder_shouldFail_whenOrderAlreadyCompleted" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.002"/>
  <testcase name="getMySoldOrders_shouldReturnEmptyPage_whenNoOrders" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.001"/>
  <testcase name="confirmContact_shouldFail_whenOrderNotConfirmed" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.001"/>
  <testcase name="completeOrder_shouldSucceed_whenBuyerCompletes" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.001"/>
  <testcase name="createOrder_shouldSucceed_whenItemIsAvailable" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.001"/>
  <testcase name="completeOrder_shouldFail_whenOrderNotConfirmed" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.002"/>
  <testcase name="confirmOrder_shouldFail_whenOrderNotPending" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0"/>
  <testcase name="confirmOrder_shouldUpdateStatusToConfirmed" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.001">
    <failure message="expected: &lt;CONFIRMED&gt; but was: &lt;AWAITING_ACKNOWLEDGEMENT&gt;" type="org.opentest4j.AssertionFailedError"><![CDATA[org.opentest4j.AssertionFailedError: expected: <CONFIRMED> but was: <AWAITING_ACKNOWLEDGEMENT>
	at com.sjtu.secondhand.service.impl.OrderServiceImplTest.confirmOrder_shouldUpdateStatusToConfirmed(OrderServiceImplTest.java:252)
]]></failure>
  </testcase>
  <testcase name="createOrder_shouldHandleRepositoryException" classname="com.sjtu.secondhand.service.impl.OrderServiceImplTest" time="0.001"/>
</testsuite>