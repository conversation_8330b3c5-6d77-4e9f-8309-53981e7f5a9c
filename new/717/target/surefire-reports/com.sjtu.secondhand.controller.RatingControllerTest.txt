-------------------------------------------------------------------------------
Test set: com.sjtu.secondhand.controller.RatingControllerTest
-------------------------------------------------------------------------------
Tests run: 15, Failures: 0, Errors: 2, Skipped: 0, Time elapsed: 0.109 s <<< FAILURE! - in com.sjtu.secondhand.controller.RatingControllerTest
createRating_ServiceThrowsRuntimeException  Time elapsed: 0.008 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: Database connection error
	at com.sjtu.secondhand.controller.RatingControllerTest.createRating_ServiceThrowsRuntimeException(RatingControllerTest.java:219)
Caused by: java.lang.RuntimeException: Database connection error
	at com.sjtu.secondhand.controller.RatingControllerTest.createRating_ServiceThrowsRuntimeException(RatingControllerTest.java:219)

createRating_ServiceThrowsApiException  Time elapsed: 0.007 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is com.sjtu.secondhand.exception.ApiException: 交易不存在
	at com.sjtu.secondhand.controller.RatingControllerTest.createRating_ServiceThrowsApiException(RatingControllerTest.java:203)
Caused by: com.sjtu.secondhand.exception.ApiException: 交易不存在
	at com.sjtu.secondhand.controller.RatingControllerTest.createRating_ServiceThrowsApiException(RatingControllerTest.java:203)

