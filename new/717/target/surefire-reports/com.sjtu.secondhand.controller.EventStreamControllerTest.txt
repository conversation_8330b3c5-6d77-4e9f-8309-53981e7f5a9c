-------------------------------------------------------------------------------
Test set: com.sjtu.secondhand.controller.EventStreamControllerTest
-------------------------------------------------------------------------------
Tests run: 12, Failures: 2, Errors: 1, Skipped: 0, Time elapsed: 0.072 s <<< FAILURE! - in com.sjtu.secondhand.controller.EventStreamControllerTest
stream_shouldHandleAddEmitterException  Time elapsed: 0.007 s  <<< ERROR!
java.lang.RuntimeException: Add emitter failed
	at com.sjtu.secondhand.controller.EventStreamControllerTest.stream_shouldHandleAddEmitterException(EventStreamControllerTest.java:235)

stream_shouldThrowException_whenTokenValidationThrowsException  Time elapsed: 0.005 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: Unexpected exception type thrown ==> expected: <java.lang.IllegalStateException> but was: <java.lang.RuntimeException>
	at com.sjtu.secondhand.controller.EventStreamControllerTest.stream_shouldThrowException_whenTokenValidationThrowsException(EventStreamControllerTest.java:147)
Caused by: java.lang.RuntimeException: JWT validation error
	at com.sjtu.secondhand.controller.EventStreamControllerTest.lambda$stream_shouldThrowException_whenTokenValidationThrowsException$0(EventStreamControllerTest.java:148)
	at com.sjtu.secondhand.controller.EventStreamControllerTest.stream_shouldThrowException_whenTokenValidationThrowsException(EventStreamControllerTest.java:147)

stream_shouldHandleTokenUserNotFound  Time elapsed: 0.005 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: Unexpected exception type thrown ==> expected: <java.lang.IllegalStateException> but was: <java.lang.RuntimeException>
	at com.sjtu.secondhand.controller.EventStreamControllerTest.stream_shouldHandleTokenUserNotFound(EventStreamControllerTest.java:218)
Caused by: java.lang.RuntimeException: User not found
	at com.sjtu.secondhand.controller.EventStreamControllerTest.lambda$stream_shouldHandleTokenUserNotFound$0(EventStreamControllerTest.java:219)
	at com.sjtu.secondhand.controller.EventStreamControllerTest.stream_shouldHandleTokenUserNotFound(EventStreamControllerTest.java:218)

