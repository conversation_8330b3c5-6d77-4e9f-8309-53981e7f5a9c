-------------------------------------------------------------------------------
Test set: com.sjtu.secondhand.service.impl.OrderServiceImplTest
-------------------------------------------------------------------------------
Tests run: 33, Failures: 4, Errors: 0, Skipped: 0, Time elapsed: 0.059 s <<< FAILURE! - in com.sjtu.secondhand.service.impl.OrderServiceImplTest
createOrder_shouldFailWhenItemTypeIsWanted  Time elapsed: 0 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: Unexpected exception type thrown ==> expected: <com.sjtu.secondhand.exception.ApiException> but was: <java.lang.NullPointerException>
	at com.sjtu.secondhand.service.impl.OrderServiceImplTest.createOrder_shouldFailWhenItemTypeIsWanted(OrderServiceImplTest.java:315)
Caused by: java.lang.NullPointerException: Cannot invoke "com.sjtu.secondhand.model.Order.getId()" because "order" is null
	at com.sjtu.secondhand.service.impl.OrderServiceImplTest.lambda$createOrder_shouldFailWhenItemTypeIsWanted$0(OrderServiceImplTest.java:315)
	at com.sjtu.secondhand.service.impl.OrderServiceImplTest.createOrder_shouldFailWhenItemTypeIsWanted(OrderServiceImplTest.java:315)

getOrderById_shouldReturnOrder_whenOrderExists  Time elapsed: 0.001 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: com.sjtu.secondhand.model.Order$OrderStatus@62d85d39<PENDING_CONFIRMATION> but was: java.lang.String@2ebd0d23<PENDING_CONFIRMATION>
	at com.sjtu.secondhand.service.impl.OrderServiceImplTest.getOrderById_shouldReturnOrder_whenOrderExists(OrderServiceImplTest.java:171)

getOrderById_shouldHandleValidOrderAccess  Time elapsed: 0.002 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: com.sjtu.secondhand.model.Order$OrderStatus@62d85d39<PENDING_CONFIRMATION> but was: java.lang.String@2ebd0d23<PENDING_CONFIRMATION>
	at com.sjtu.secondhand.service.impl.OrderServiceImplTest.getOrderById_shouldHandleValidOrderAccess(OrderServiceImplTest.java:229)

confirmOrder_shouldUpdateStatusToConfirmed  Time elapsed: 0.001 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <CONFIRMED> but was: <AWAITING_ACKNOWLEDGEMENT>
	at com.sjtu.secondhand.service.impl.OrderServiceImplTest.confirmOrder_shouldUpdateStatusToConfirmed(OrderServiceImplTest.java:252)

