-------------------------------------------------------------------------------
Test set: com.sjtu.secondhand.service.impl.FootprintServiceImplTest
-------------------------------------------------------------------------------
Tests run: 15, Failures: 5, Errors: 0, Skipped: 0, Time elapsed: 0.02 s <<< FAILURE! - in com.sjtu.secondhand.service.impl.FootprintServiceImplTest
deleteFootprint_UserNotFound  Time elapsed: 0.003 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: Expected com.sjtu.secondhand.exception.ApiException to be thrown, but nothing was thrown.
	at com.sjtu.secondhand.service.impl.FootprintServiceImplTest.deleteFootprint_UserNotFound(FootprintServiceImplTest.java:225)

recordFootprint_UserNotFound  Time elapsed: 0 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: Expected com.sjtu.secondhand.exception.ApiException to be thrown, but nothing was thrown.
	at com.sjtu.secondhand.service.impl.FootprintServiceImplTest.recordFootprint_UserNotFound(FootprintServiceImplTest.java:119)

recordFootprint_ItemNotFound  Time elapsed: 0.002 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: Expected com.sjtu.secondhand.exception.ApiException to be thrown, but nothing was thrown.
	at com.sjtu.secondhand.service.impl.FootprintServiceImplTest.recordFootprint_ItemNotFound(FootprintServiceImplTest.java:132)

getUserFootprints_VerifyResponseStructure  Time elapsed: 0.003 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <2025-07-18T12:35:14.220600> but was: <2025-07-18 12:35:14>
	at com.sjtu.secondhand.service.impl.FootprintServiceImplTest.getUserFootprints_VerifyResponseStructure(FootprintServiceImplTest.java:317)

clearAllFootprints_UserNotFound  Time elapsed: 0.005 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: Expected com.sjtu.secondhand.exception.ApiException to be thrown, but nothing was thrown.
	at com.sjtu.secondhand.service.impl.FootprintServiceImplTest.clearAllFootprints_UserNotFound(FootprintServiceImplTest.java:264)

