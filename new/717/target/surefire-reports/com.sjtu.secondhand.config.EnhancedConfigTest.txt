-------------------------------------------------------------------------------
Test set: com.sjtu.secondhand.config.EnhancedConfigTest
-------------------------------------------------------------------------------
Tests run: 9, Failures: 0, Errors: 2, Skipped: 0, Time elapsed: 0.472 s <<< FAILURE! - in com.sjtu.secondhand.config.EnhancedConfigTest
testJacksonConfigDetailedConfiguration  Time elapsed: 0.008 s  <<< ERROR!
com.fasterxml.jackson.databind.exc.InvalidFormatException: 
Cannot deserialize value of type `java.time.LocalDateTime` from String "2024-01-15 14:30:45": Failed to deserialize java.time.LocalDateTime: (java.time.format.DateTimeParseException) Text '2024-01-15 14:30:45' could not be parsed at index 10
 at [Source: (String)""2024-01-15 14:30:45""; line: 1, column: 1]
	at com.sjtu.secondhand.config.EnhancedConfigTest.testJacksonConfigDetailedConfiguration(EnhancedConfigTest.java:71)
Caused by: java.time.format.DateTimeParseException: Text '2024-01-15 14:30:45' could not be parsed at index 10
	at com.sjtu.secondhand.config.EnhancedConfigTest.testJacksonConfigDetailedConfiguration(EnhancedConfigTest.java:71)

testSecurityConfigBeanCreation  Time elapsed: 0.295 s  <<< ERROR!
org.springframework.beans.factory.NoUniqueBeanDefinitionException: No qualifying bean of type 'org.springframework.web.cors.CorsConfigurationSource' available: expected single matching bean but found 2: corsConfigurationSource,mvcHandlerMappingIntrospector
	at com.sjtu.secondhand.config.EnhancedConfigTest.testSecurityConfigBeanCreation(EnhancedConfigTest.java:137)

