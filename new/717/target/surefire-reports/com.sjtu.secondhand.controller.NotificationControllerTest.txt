-------------------------------------------------------------------------------
Test set: com.sjtu.secondhand.controller.NotificationControllerTest
-------------------------------------------------------------------------------
Tests run: 42, Failures: 0, Errors: 26, Skipped: 0, Time elapsed: 0.247 s <<< FAILURE! - in com.sjtu.secondhand.controller.NotificationControllerTest
getAllNotifications_shouldReturnUnauthorized_whenNotAuthenticated  Time elapsed: 0.013 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "org.springframework.security.core.Authentication.getName()" because "authentication" is null
	at com.sjtu.secondhand.controller.NotificationControllerTest.getAllNotifications_shouldReturnUnauthorized_whenNotAuthenticated(NotificationControllerTest.java:403)
Caused by: java.lang.NullPointerException: Cannot invoke "org.springframework.security.core.Authentication.getName()" because "authentication" is null
	at com.sjtu.secondhand.controller.NotificationControllerTest.getAllNotifications_shouldReturnUnauthorized_whenNotAuthenticated(NotificationControllerTest.java:403)

deleteNotification_shouldReturnError_whenNotAuthorized  Time elapsed: 0.007 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.SecurityException: 用户无权限删除此通知
	at com.sjtu.secondhand.controller.NotificationControllerTest.deleteNotification_shouldReturnError_whenNotAuthorized(NotificationControllerTest.java:473)
Caused by: java.lang.SecurityException: 用户无权限删除此通知
	at com.sjtu.secondhand.controller.NotificationControllerTest.deleteNotification_shouldReturnError_whenNotAuthorized(NotificationControllerTest.java:473)

deleteNotification_shouldReturnError_whenNotificationNotFound  Time elapsed: 0.006 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: 通知不存在
	at com.sjtu.secondhand.controller.NotificationControllerTest.deleteNotification_shouldReturnError_whenNotificationNotFound(NotificationControllerTest.java:258)
Caused by: java.lang.RuntimeException: 通知不存在
	at com.sjtu.secondhand.controller.NotificationControllerTest.deleteNotification_shouldReturnError_whenNotificationNotFound(NotificationControllerTest.java:258)

deleteNotification_shouldHandleConcurrentDeletion  Time elapsed: 0.006 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: Notification already deleted
	at com.sjtu.secondhand.controller.NotificationControllerTest.deleteNotification_shouldHandleConcurrentDeletion(NotificationControllerTest.java:587)
Caused by: java.lang.RuntimeException: Notification already deleted
	at com.sjtu.secondhand.controller.NotificationControllerTest.deleteNotification_shouldHandleConcurrentDeletion(NotificationControllerTest.java:587)

markAsRead_shouldReturnError_whenNotificationNotFound  Time elapsed: 0.005 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: 通知不存在
	at com.sjtu.secondhand.controller.NotificationControllerTest.markAsRead_shouldReturnError_whenNotificationNotFound(NotificationControllerTest.java:221)
Caused by: java.lang.RuntimeException: 通知不存在
	at com.sjtu.secondhand.controller.NotificationControllerTest.markAsRead_shouldReturnError_whenNotificationNotFound(NotificationControllerTest.java:221)

markAllAsRead_shouldHandleConcurrentModification  Time elapsed: 0.004 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: Concurrent modification
	at com.sjtu.secondhand.controller.NotificationControllerTest.markAllAsRead_shouldHandleConcurrentModification(NotificationControllerTest.java:575)
Caused by: java.lang.RuntimeException: Concurrent modification
	at com.sjtu.secondhand.controller.NotificationControllerTest.markAllAsRead_shouldHandleConcurrentModification(NotificationControllerTest.java:575)

getAllNotifications_shouldReturnError_whenServiceThrowsException  Time elapsed: 0.005 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: Database connection failed
	at com.sjtu.secondhand.controller.NotificationControllerTest.getAllNotifications_shouldReturnError_whenServiceThrowsException(NotificationControllerTest.java:332)
Caused by: java.lang.RuntimeException: Database connection failed
	at com.sjtu.secondhand.controller.NotificationControllerTest.getAllNotifications_shouldReturnError_whenServiceThrowsException(NotificationControllerTest.java:332)

deleteNotification_shouldHandleInvalidNotificationId  Time elapsed: 0.006 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.IllegalArgumentException: Invalid notification ID
	at com.sjtu.secondhand.controller.NotificationControllerTest.deleteNotification_shouldHandleInvalidNotificationId(NotificationControllerTest.java:393)
Caused by: java.lang.IllegalArgumentException: Invalid notification ID
	at com.sjtu.secondhand.controller.NotificationControllerTest.deleteNotification_shouldHandleInvalidNotificationId(NotificationControllerTest.java:393)

markAllAsRead_shouldReturnError_whenUserNotFound  Time elapsed: 0.005 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: 用户不存在
	at com.sjtu.secondhand.controller.NotificationControllerTest.markAllAsRead_shouldReturnError_whenUserNotFound(NotificationControllerTest.java:309)
Caused by: java.lang.RuntimeException: 用户不存在
	at com.sjtu.secondhand.controller.NotificationControllerTest.markAllAsRead_shouldReturnError_whenUserNotFound(NotificationControllerTest.java:309)

deleteNotification_shouldReturnError_whenUserNotFound  Time elapsed: 0.006 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: 用户不存在
	at com.sjtu.secondhand.controller.NotificationControllerTest.deleteNotification_shouldReturnError_whenUserNotFound(NotificationControllerTest.java:319)
Caused by: java.lang.RuntimeException: 用户不存在
	at com.sjtu.secondhand.controller.NotificationControllerTest.deleteNotification_shouldReturnError_whenUserNotFound(NotificationControllerTest.java:319)

countUnreadNotifications_shouldReturnUnauthorized_whenNotAuthenticated  Time elapsed: 0.003 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "org.springframework.security.core.Authentication.getName()" because "authentication" is null
	at com.sjtu.secondhand.controller.NotificationControllerTest.countUnreadNotifications_shouldReturnUnauthorized_whenNotAuthenticated(NotificationControllerTest.java:421)
Caused by: java.lang.NullPointerException: Cannot invoke "org.springframework.security.core.Authentication.getName()" because "authentication" is null
	at com.sjtu.secondhand.controller.NotificationControllerTest.countUnreadNotifications_shouldReturnUnauthorized_whenNotAuthenticated(NotificationControllerTest.java:421)

markAsRead_shouldHandleZeroNotificationId  Time elapsed: 0.005 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.IllegalArgumentException: Notification ID must be positive
	at com.sjtu.secondhand.controller.NotificationControllerTest.markAsRead_shouldHandleZeroNotificationId(NotificationControllerTest.java:486)
Caused by: java.lang.IllegalArgumentException: Notification ID must be positive
	at com.sjtu.secondhand.controller.NotificationControllerTest.markAsRead_shouldHandleZeroNotificationId(NotificationControllerTest.java:486)

markAsRead_shouldReturnError_whenNotAuthorized  Time elapsed: 0.005 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.SecurityException: 用户无权限访问此通知
	at com.sjtu.secondhand.controller.NotificationControllerTest.markAsRead_shouldReturnError_whenNotAuthorized(NotificationControllerTest.java:461)
Caused by: java.lang.SecurityException: 用户无权限访问此通知
	at com.sjtu.secondhand.controller.NotificationControllerTest.markAsRead_shouldReturnError_whenNotAuthorized(NotificationControllerTest.java:461)

getAllNotifications_shouldThrowException_whenUserNotFound  Time elapsed: 0.007 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: 用户不存在
	at com.sjtu.secondhand.controller.NotificationControllerTest.getAllNotifications_shouldThrowException_whenUserNotFound(NotificationControllerTest.java:269)
Caused by: java.lang.RuntimeException: 用户不存在
	at com.sjtu.secondhand.controller.NotificationControllerTest.getAllNotifications_shouldThrowException_whenUserNotFound(NotificationControllerTest.java:269)

markAsRead_shouldReturnUnauthorized_whenNotAuthenticated  Time elapsed: 0.003 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "org.springframework.security.core.Authentication.getName()" because "authentication" is null
	at com.sjtu.secondhand.controller.NotificationControllerTest.markAsRead_shouldReturnUnauthorized_whenNotAuthenticated(NotificationControllerTest.java:430)
Caused by: java.lang.NullPointerException: Cannot invoke "org.springframework.security.core.Authentication.getName()" because "authentication" is null
	at com.sjtu.secondhand.controller.NotificationControllerTest.markAsRead_shouldReturnUnauthorized_whenNotAuthenticated(NotificationControllerTest.java:430)

getUnreadNotifications_shouldReturnUnauthorized_whenNotAuthenticated  Time elapsed: 0.004 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "org.springframework.security.core.Authentication.getName()" because "authentication" is null
	at com.sjtu.secondhand.controller.NotificationControllerTest.getUnreadNotifications_shouldReturnUnauthorized_whenNotAuthenticated(NotificationControllerTest.java:412)
Caused by: java.lang.NullPointerException: Cannot invoke "org.springframework.security.core.Authentication.getName()" because "authentication" is null
	at com.sjtu.secondhand.controller.NotificationControllerTest.getUnreadNotifications_shouldReturnUnauthorized_whenNotAuthenticated(NotificationControllerTest.java:412)

deleteNotification_shouldHandleZeroNotificationId  Time elapsed: 0.005 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.IllegalArgumentException: Notification ID must be positive
	at com.sjtu.secondhand.controller.NotificationControllerTest.deleteNotification_shouldHandleZeroNotificationId(NotificationControllerTest.java:498)
Caused by: java.lang.IllegalArgumentException: Notification ID must be positive
	at com.sjtu.secondhand.controller.NotificationControllerTest.deleteNotification_shouldHandleZeroNotificationId(NotificationControllerTest.java:498)

markAllAsRead_shouldReturnUnauthorized_whenNotAuthenticated  Time elapsed: 0.005 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "org.springframework.security.core.Authentication.getName()" because "authentication" is null
	at com.sjtu.secondhand.controller.NotificationControllerTest.markAllAsRead_shouldReturnUnauthorized_whenNotAuthenticated(NotificationControllerTest.java:439)
Caused by: java.lang.NullPointerException: Cannot invoke "org.springframework.security.core.Authentication.getName()" because "authentication" is null
	at com.sjtu.secondhand.controller.NotificationControllerTest.markAllAsRead_shouldReturnUnauthorized_whenNotAuthenticated(NotificationControllerTest.java:439)

markAsRead_shouldReturnError_whenUserNotFound  Time elapsed: 0.003 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: 用户不存在
	at com.sjtu.secondhand.controller.NotificationControllerTest.markAsRead_shouldReturnError_whenUserNotFound(NotificationControllerTest.java:299)
Caused by: java.lang.RuntimeException: 用户不存在
	at com.sjtu.secondhand.controller.NotificationControllerTest.markAsRead_shouldReturnError_whenUserNotFound(NotificationControllerTest.java:299)

countUnreadNotifications_shouldReturnError_whenUserNotFound  Time elapsed: 0.004 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: 用户不存在
	at com.sjtu.secondhand.controller.NotificationControllerTest.countUnreadNotifications_shouldReturnError_whenUserNotFound(NotificationControllerTest.java:289)
Caused by: java.lang.RuntimeException: 用户不存在
	at com.sjtu.secondhand.controller.NotificationControllerTest.countUnreadNotifications_shouldReturnError_whenUserNotFound(NotificationControllerTest.java:289)

markAllAsRead_shouldReturnError_whenServiceThrowsException  Time elapsed: 0.004 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: Database connection failed
	at com.sjtu.secondhand.controller.NotificationControllerTest.markAllAsRead_shouldReturnError_whenServiceThrowsException(NotificationControllerTest.java:368)
Caused by: java.lang.RuntimeException: Database connection failed
	at com.sjtu.secondhand.controller.NotificationControllerTest.markAllAsRead_shouldReturnError_whenServiceThrowsException(NotificationControllerTest.java:368)

countUnreadNotifications_shouldReturnError_whenServiceThrowsException  Time elapsed: 0.005 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: Database connection failed
	at com.sjtu.secondhand.controller.NotificationControllerTest.countUnreadNotifications_shouldReturnError_whenServiceThrowsException(NotificationControllerTest.java:356)
Caused by: java.lang.RuntimeException: Database connection failed
	at com.sjtu.secondhand.controller.NotificationControllerTest.countUnreadNotifications_shouldReturnError_whenServiceThrowsException(NotificationControllerTest.java:356)

deleteNotification_shouldReturnUnauthorized_whenNotAuthenticated  Time elapsed: 0.004 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.NullPointerException: Cannot invoke "org.springframework.security.core.Authentication.getName()" because "authentication" is null
	at com.sjtu.secondhand.controller.NotificationControllerTest.deleteNotification_shouldReturnUnauthorized_whenNotAuthenticated(NotificationControllerTest.java:448)
Caused by: java.lang.NullPointerException: Cannot invoke "org.springframework.security.core.Authentication.getName()" because "authentication" is null
	at com.sjtu.secondhand.controller.NotificationControllerTest.deleteNotification_shouldReturnUnauthorized_whenNotAuthenticated(NotificationControllerTest.java:448)

getUnreadNotifications_shouldReturnError_whenServiceThrowsException  Time elapsed: 0.011 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: Database connection failed
	at com.sjtu.secondhand.controller.NotificationControllerTest.getUnreadNotifications_shouldReturnError_whenServiceThrowsException(NotificationControllerTest.java:344)
Caused by: java.lang.RuntimeException: Database connection failed
	at com.sjtu.secondhand.controller.NotificationControllerTest.getUnreadNotifications_shouldReturnError_whenServiceThrowsException(NotificationControllerTest.java:344)

markAsRead_shouldHandleInvalidNotificationId  Time elapsed: 0.004 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.IllegalArgumentException: Invalid notification ID
	at com.sjtu.secondhand.controller.NotificationControllerTest.markAsRead_shouldHandleInvalidNotificationId(NotificationControllerTest.java:381)
Caused by: java.lang.IllegalArgumentException: Invalid notification ID
	at com.sjtu.secondhand.controller.NotificationControllerTest.markAsRead_shouldHandleInvalidNotificationId(NotificationControllerTest.java:381)

getUnreadNotifications_shouldReturnError_whenUserNotFound  Time elapsed: 0.006 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: 用户不存在
	at com.sjtu.secondhand.controller.NotificationControllerTest.getUnreadNotifications_shouldReturnError_whenUserNotFound(NotificationControllerTest.java:279)
Caused by: java.lang.RuntimeException: 用户不存在
	at com.sjtu.secondhand.controller.NotificationControllerTest.getUnreadNotifications_shouldReturnError_whenUserNotFound(NotificationControllerTest.java:279)

