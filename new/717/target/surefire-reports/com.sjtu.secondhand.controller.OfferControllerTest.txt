-------------------------------------------------------------------------------
Test set: com.sjtu.secondhand.controller.OfferControllerTest
-------------------------------------------------------------------------------
Tests run: 19, Failures: 0, Errors: 11, Skipped: 0, Time elapsed: 0.136 s <<< FAILURE! - in com.sjtu.secondhand.controller.OfferControllerTest
acceptOffer_PermissionDenied  Time elapsed: 0.004 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is com.sjtu.secondhand.exception.ApiException: 只有求购者可以接受Offer
	at com.sjtu.secondhand.controller.OfferControllerTest.acceptOffer_PermissionDenied(OfferControllerTest.java:145)
Caused by: com.sjtu.secondhand.exception.ApiException: 只有求购者可以接受Offer
	at com.sjtu.secondhand.controller.OfferControllerTest.acceptOffer_PermissionDenied(OfferControllerTest.java:145)

rejectOffer_InvalidStatus  Time elapsed: 0.005 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is com.sjtu.secondhand.exception.ApiException: 只有待接受的Offer可以被拒绝
	at com.sjtu.secondhand.controller.OfferControllerTest.rejectOffer_InvalidStatus(OfferControllerTest.java:177)
Caused by: com.sjtu.secondhand.exception.ApiException: 只有待接受的Offer可以被拒绝
	at com.sjtu.secondhand.controller.OfferControllerTest.rejectOffer_InvalidStatus(OfferControllerTest.java:177)

rejectOffer_ZeroId  Time elapsed: 0.009 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is com.sjtu.secondhand.exception.ApiException: Offer不存在
	at com.sjtu.secondhand.controller.OfferControllerTest.rejectOffer_ZeroId(OfferControllerTest.java:333)
Caused by: com.sjtu.secondhand.exception.ApiException: Offer不存在
	at com.sjtu.secondhand.controller.OfferControllerTest.rejectOffer_ZeroId(OfferControllerTest.java:333)

cancelOffer_PermissionDenied  Time elapsed: 0.005 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is com.sjtu.secondhand.exception.ApiException: 只有交易参与方可以取消交易
	at com.sjtu.secondhand.controller.OfferControllerTest.cancelOffer_PermissionDenied(OfferControllerTest.java:269)
Caused by: com.sjtu.secondhand.exception.ApiException: 只有交易参与方可以取消交易
	at com.sjtu.secondhand.controller.OfferControllerTest.cancelOffer_PermissionDenied(OfferControllerTest.java:269)

acceptOffer_NegativeId  Time elapsed: 0.007 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is com.sjtu.secondhand.exception.ApiException: Offer不存在
	at com.sjtu.secondhand.controller.OfferControllerTest.acceptOffer_NegativeId(OfferControllerTest.java:319)
Caused by: com.sjtu.secondhand.exception.ApiException: Offer不存在
	at com.sjtu.secondhand.controller.OfferControllerTest.acceptOffer_NegativeId(OfferControllerTest.java:319)

confirmOffer_InvalidStatus  Time elapsed: 0.004 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is com.sjtu.secondhand.exception.ApiException: 只有已接受的Offer可以被确认
	at com.sjtu.secondhand.controller.OfferControllerTest.confirmOffer_InvalidStatus(OfferControllerTest.java:223)
Caused by: com.sjtu.secondhand.exception.ApiException: 只有已接受的Offer可以被确认
	at com.sjtu.secondhand.controller.OfferControllerTest.confirmOffer_InvalidStatus(OfferControllerTest.java:223)

confirmOffer_PermissionDenied  Time elapsed: 0.004 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is com.sjtu.secondhand.exception.ApiException: 只有响应者可以确认Offer
	at com.sjtu.secondhand.controller.OfferControllerTest.confirmOffer_PermissionDenied(OfferControllerTest.java:209)
Caused by: com.sjtu.secondhand.exception.ApiException: 只有响应者可以确认Offer
	at com.sjtu.secondhand.controller.OfferControllerTest.confirmOffer_PermissionDenied(OfferControllerTest.java:209)

acceptOffer_OfferNotFound  Time elapsed: 0.005 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is com.sjtu.secondhand.exception.ApiException: Offer不存在
	at com.sjtu.secondhand.controller.OfferControllerTest.acceptOffer_OfferNotFound(OfferControllerTest.java:131)
Caused by: com.sjtu.secondhand.exception.ApiException: Offer不存在
	at com.sjtu.secondhand.controller.OfferControllerTest.acceptOffer_OfferNotFound(OfferControllerTest.java:131)

createOffer_ServiceTimeout  Time elapsed: 0.006 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is java.lang.RuntimeException: Service timeout
	at com.sjtu.secondhand.controller.OfferControllerTest.createOffer_ServiceTimeout(OfferControllerTest.java:349)
Caused by: java.lang.RuntimeException: Service timeout
	at com.sjtu.secondhand.controller.OfferControllerTest.createOffer_ServiceTimeout(OfferControllerTest.java:349)

createOffer_ServiceException  Time elapsed: 0.008 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is com.sjtu.secondhand.exception.ApiException: 只能对求购类型的物品创建Offer
	at com.sjtu.secondhand.controller.OfferControllerTest.createOffer_ServiceException(OfferControllerTest.java:96)
Caused by: com.sjtu.secondhand.exception.ApiException: 只能对求购类型的物品创建Offer
	at com.sjtu.secondhand.controller.OfferControllerTest.createOffer_ServiceException(OfferControllerTest.java:96)

cancelOffer_AlreadyCompleted  Time elapsed: 0.006 s  <<< ERROR!
org.springframework.web.util.NestedServletException: Request processing failed; nested exception is com.sjtu.secondhand.exception.ApiException: 已完成或已取消的Offer不能被取消
	at com.sjtu.secondhand.controller.OfferControllerTest.cancelOffer_AlreadyCompleted(OfferControllerTest.java:255)
Caused by: com.sjtu.secondhand.exception.ApiException: 已完成或已取消的Offer不能被取消
	at com.sjtu.secondhand.controller.OfferControllerTest.cancelOffer_AlreadyCompleted(OfferControllerTest.java:255)

