-------------------------------------------------------------------------------
Test set: com.sjtu.secondhand.service.impl.RecommendationServiceImplTest
-------------------------------------------------------------------------------
Tests run: 15, Failures: 1, Errors: 9, Skipped: 0, Time elapsed: 0.045 s <<< FAILURE! - in com.sjtu.secondhand.service.impl.RecommendationServiceImplTest
getItemCFRecommendations_WithFavorites_Success  Time elapsed: 0.003 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForList' method:
    jdbcTemplate.queryForList(
    "SELECT i.id FROM items i JOIN users u ON i.user_id = u.id WHERE i.status = 'FOR_SALE' AND i.is_visible = TRUE ORDER BY (5.0 * i.favorite_count + 1.0 * i.view_count + 0.1 * u.credit_score + 0.1 * u.points) DESC LIMIT ?",
    class java.lang.Long,
    16
);
    -> at com.sjtu.secondhand.service.impl.RecommendationServiceImpl.getHotRecommendations(RecommendationServiceImpl.java:95)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForList("", null, 0L, 0);
      -> at com.sjtu.secondhand.service.impl.RecommendationServiceImplTest.getItemCFRecommendations_WithFavorites_Success(RecommendationServiceImplTest.java:314)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.sjtu.secondhand.service.impl.RecommendationServiceImplTest.getItemCFRecommendations_WithFavorites_Success(RecommendationServiceImplTest.java:360)

getItemCFRecommendations_InsufficientRecommendations_FallbackToHot  Time elapsed: 0 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForList' method:
    jdbcTemplate.queryForList(
    "SELECT i.id FROM items i JOIN users u ON i.user_id = u.id WHERE i.status = 'FOR_SALE' AND i.is_visible = TRUE ORDER BY (5.0 * i.favorite_count + 1.0 * i.view_count + 0.1 * u.credit_score + 0.1 * u.points) DESC LIMIT ?",
    class java.lang.Long,
    10
);
    -> at com.sjtu.secondhand.service.impl.RecommendationServiceImpl.getHotRecommendations(RecommendationServiceImpl.java:95)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForList("", null, 0L, 0);
      -> at com.sjtu.secondhand.service.impl.RecommendationServiceImplTest.getItemCFRecommendations_InsufficientRecommendations_FallbackToHot(RecommendationServiceImplTest.java:381)
    2. jdbcTemplate.queryForList("", null, 0);
      -> at com.sjtu.secondhand.service.impl.RecommendationServiceImplTest.getItemCFRecommendations_InsufficientRecommendations_FallbackToHot(RecommendationServiceImplTest.java:403)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.sjtu.secondhand.service.impl.RecommendationServiceImplTest.getItemCFRecommendations_InsufficientRecommendations_FallbackToHot(RecommendationServiceImplTest.java:423)

getItemCFRecommendations_WithException_ReturnsEmpty  Time elapsed: 0.003 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForList' method:
    jdbcTemplate.queryForList(
    "SELECT i.id FROM items i JOIN users u ON i.user_id = u.id WHERE i.status = 'FOR_SALE' AND i.is_visible = TRUE ORDER BY (5.0 * i.favorite_count + 1.0 * i.view_count + 0.1 * u.credit_score + 0.1 * u.points) DESC LIMIT ?",
    class java.lang.Long,
    16
);
    -> at com.sjtu.secondhand.service.impl.RecommendationServiceImpl.getHotRecommendations(RecommendationServiceImpl.java:95)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForList("", null, 0L, 0);
      -> at com.sjtu.secondhand.service.impl.RecommendationServiceImplTest.getItemCFRecommendations_WithException_ReturnsEmpty(RecommendationServiceImplTest.java:153)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.sjtu.secondhand.service.impl.RecommendationServiceImplTest.getItemCFRecommendations_WithException_ReturnsEmpty(RecommendationServiceImplTest.java:157)

getContentBasedRecommendations_ItemNotFound  Time elapsed: 0.005 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.service.impl.RecommendationServiceImplTest.getContentBasedRecommendations_ItemNotFound(RecommendationServiceImplTest.java:213)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

getContentBasedRecommendations_NoSimilarItems_UsesFallback  Time elapsed: 0.005 s  <<< FAILURE!
org.mockito.exceptions.verification.TooManyActualInvocations: 

itemRepository.findById(1L);
Wanted 1 time:
-> at com.sjtu.secondhand.service.impl.RecommendationServiceImplTest.getContentBasedRecommendations_NoSimilarItems_UsesFallback(RecommendationServiceImplTest.java:207)
But was 2 times:
-> at com.sjtu.secondhand.service.impl.RecommendationServiceImpl.getContentBasedRecommendations(RecommendationServiceImpl.java:222)
-> at com.sjtu.secondhand.service.impl.RecommendationServiceImpl.getContentBasedRecommendationsFallback(RecommendationServiceImpl.java:292)


	at com.sjtu.secondhand.service.impl.RecommendationServiceImplTest.getContentBasedRecommendations_NoSimilarItems_UsesFallback(RecommendationServiceImplTest.java:207)

getItemCFRecommendations_NoFavorites  Time elapsed: 0.003 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForList' method:
    jdbcTemplate.queryForList(
    "SELECT i.id FROM items i JOIN users u ON i.user_id = u.id WHERE i.status = 'FOR_SALE' AND i.is_visible = TRUE ORDER BY (5.0 * i.favorite_count + 1.0 * i.view_count + 0.1 * u.credit_score + 0.1 * u.points) DESC LIMIT ?",
    class java.lang.Long,
    16
);
    -> at com.sjtu.secondhand.service.impl.RecommendationServiceImpl.getHotRecommendations(RecommendationServiceImpl.java:95)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForList("", null, 0L, 0);
      -> at com.sjtu.secondhand.service.impl.RecommendationServiceImplTest.getItemCFRecommendations_NoFavorites(RecommendationServiceImplTest.java:138)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.sjtu.secondhand.service.impl.RecommendationServiceImplTest.getItemCFRecommendations_NoFavorites(RecommendationServiceImplTest.java:142)

getItemCFRecommendations_DefaultLimit  Time elapsed: 0 s  <<< ERROR!
org.mockito.exceptions.misusing.PotentialStubbingProblem: 

Strict stubbing argument mismatch. Please check:
 - this invocation of 'queryForList' method:
    jdbcTemplate.queryForList(
    "SELECT i.id FROM items i JOIN users u ON i.user_id = u.id WHERE i.status = 'FOR_SALE' AND i.is_visible = TRUE ORDER BY (5.0 * i.favorite_count + 1.0 * i.view_count + 0.1 * u.credit_score + 0.1 * u.points) DESC LIMIT ?",
    class java.lang.Long,
    16
);
    -> at com.sjtu.secondhand.service.impl.RecommendationServiceImpl.getHotRecommendations(RecommendationServiceImpl.java:95)
 - has following stubbing(s) with different arguments:
    1. jdbcTemplate.queryForList("", null, 0L, 0);
      -> at com.sjtu.secondhand.service.impl.RecommendationServiceImplTest.getItemCFRecommendations_DefaultLimit(RecommendationServiceImplTest.java:498)
Typically, stubbing argument mismatch indicates user mistake when writing tests.
Mockito fails early so that you can debug potential problem easily.
However, there are legit scenarios when this exception generates false negative signal:
  - stubbing the same method multiple times using 'given().will()' or 'when().then()' API
    Please use 'will().given()' or 'doReturn().when()' API for stubbing.
  - stubbed method is intentionally invoked with different arguments by code under test
    Please use default or 'silent' JUnit Rule (equivalent of Strictness.LENIENT).
For more information see javadoc for PotentialStubbingProblem class.
	at com.sjtu.secondhand.service.impl.RecommendationServiceImplTest.getItemCFRecommendations_DefaultLimit(RecommendationServiceImplTest.java:502)

getContentBasedRecommendations_Success  Time elapsed: 0.001 s  <<< ERROR!
com.sjtu.secondhand.exception.ApiException: 物品不存在
	at com.sjtu.secondhand.service.impl.RecommendationServiceImplTest.getContentBasedRecommendations_Success(RecommendationServiceImplTest.java:177)

getContentBasedRecommendations_MultipleResults  Time elapsed: 0.003 s  <<< ERROR!
com.sjtu.secondhand.exception.ApiException: 物品不存在
	at com.sjtu.secondhand.service.impl.RecommendationServiceImplTest.getContentBasedRecommendations_MultipleResults(RecommendationServiceImplTest.java:295)

getContentBasedRecommendations_DefaultLimit  Time elapsed: 0.002 s  <<< ERROR!
com.sjtu.secondhand.exception.ApiException: 物品不存在
	at com.sjtu.secondhand.service.impl.RecommendationServiceImplTest.getContentBasedRecommendations_DefaultLimit(RecommendationServiceImplTest.java:486)

