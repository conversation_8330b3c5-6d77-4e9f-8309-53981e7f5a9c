-------------------------------------------------------------------------------
Test set: com.sjtu.secondhand.controller.UploadControllerTest
-------------------------------------------------------------------------------
Tests run: 27, Failures: 11, Errors: 1, Skipped: 0, Time elapsed: 0.198 s <<< FAILURE! - in com.sjtu.secondhand.controller.UploadControllerTest
updateOssCors_shouldReturnUnauthorized_whenNotAuthenticated  Time elapsed: 0.004 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<401> but was:<200>
	at com.sjtu.secondhand.controller.UploadControllerTest.updateOssCors_shouldReturnUnauthorized_whenNotAuthenticated(UploadControllerTest.java:465)

deleteFile_shouldReturnUnauthorized_whenNotAuthenticated  Time elapsed: 0.005 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<401> but was:<200>
	at com.sjtu.secondhand.controller.UploadControllerTest.deleteFile_shouldReturnUnauthorized_whenNotAuthenticated(UploadControllerTest.java:449)

getRequestInfo_shouldIncludeAllRequestDetails  Time elapsed: 0.007 s  <<< FAILURE!
java.lang.AssertionError: JSON path "$.data.queryString" expected:<test=value> but was:<null>
	at com.sjtu.secondhand.controller.UploadControllerTest.getRequestInfo_shouldIncludeAllRequestDetails(UploadControllerTest.java:414)

getOssInfo_shouldReturnUnauthorized_whenNotAuthenticated  Time elapsed: 0.005 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<401> but was:<200>
	at com.sjtu.secondhand.controller.UploadControllerTest.getOssInfo_shouldReturnUnauthorized_whenNotAuthenticated(UploadControllerTest.java:457)

getOssInfo_shouldReturnOssInfo_whenOssStorage  Time elapsed: 0.008 s  <<< FAILURE!
java.lang.AssertionError: JSON path "$.success" expected:<true> but was:<false>
	at com.sjtu.secondhand.controller.UploadControllerTest.getOssInfo_shouldReturnOssInfo_whenOssStorage(UploadControllerTest.java:277)

updateOssCors_shouldUpdateCors_whenOssStorage  Time elapsed: 0.006 s  <<< ERROR!
org.mockito.exceptions.base.MockitoException: 

Only void methods can doNothing()!
Example of correct use of doNothing():
    doNothing().
    doThrow(new RuntimeException())
    .when(mock).someVoidMethod();
Above means:
someVoidMethod() does nothing the 1st time but throws an exception the 2nd time is called
	at com.sjtu.secondhand.controller.UploadControllerTest.updateOssCors_shouldUpdateCors_whenOssStorage(UploadControllerTest.java:343)

getOssInfo_shouldReturnError_whenOssClientThrowsException  Time elapsed: 0.005 s  <<< FAILURE!
java.lang.AssertionError: JSON path "$.message" expected:<获取OSS配置信息失败: OSS连接失败> but was:<获取OSS配置信息失败: Cannot invoke "com.aliyun.oss.OSS.getBucketCORSRules(String)" because "this.ossClient" is null>
	at com.sjtu.secondhand.controller.UploadControllerTest.getOssInfo_shouldReturnError_whenOssClientThrowsException(UploadControllerTest.java:316)

getUploadSignature_shouldReturnUnauthorized_whenNotAuthenticated  Time elapsed: 0.006 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<401> but was:<200>
	at com.sjtu.secondhand.controller.UploadControllerTest.getUploadSignature_shouldReturnUnauthorized_whenNotAuthenticated(UploadControllerTest.java:428)

getRequestInfo_shouldReturnUnauthorized_whenNotAuthenticated  Time elapsed: 0.005 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<401> but was:<200>
	at com.sjtu.secondhand.controller.UploadControllerTest.getRequestInfo_shouldReturnUnauthorized_whenNotAuthenticated(UploadControllerTest.java:473)

updateOssCors_shouldReturnError_whenOssClientThrowsException  Time elapsed: 0.007 s  <<< FAILURE!
java.lang.AssertionError: JSON path "$.message" expected:<更新OSS CORS规则失败: 更新CORS失败> but was:<更新OSS CORS规则失败: Cannot invoke "com.aliyun.oss.OSS.setBucketCORS(com.aliyun.oss.model.SetBucketCORSRequest)" because "this.ossClient" is null>
	at com.sjtu.secondhand.controller.UploadControllerTest.updateOssCors_shouldReturnError_whenOssClientThrowsException(UploadControllerTest.java:385)

uploadFile_shouldReturnUnauthorized_whenNotAuthenticated  Time elapsed: 0.008 s  <<< FAILURE!
java.lang.AssertionError: Status expected:<401> but was:<200>
	at com.sjtu.secondhand.controller.UploadControllerTest.uploadFile_shouldReturnUnauthorized_whenNotAuthenticated(UploadControllerTest.java:440)

getOssInfo_shouldHandleNullCorsRules  Time elapsed: 0.006 s  <<< FAILURE!
java.lang.AssertionError: JSON path "$.success" expected:<true> but was:<false>
	at com.sjtu.secondhand.controller.UploadControllerTest.getOssInfo_shouldHandleNullCorsRules(UploadControllerTest.java:331)

