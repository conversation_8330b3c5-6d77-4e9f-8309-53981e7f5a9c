# Created at 2025-07-18T12:34:50.224
java.lang.instrument.IllegalClassFormatException: Error while instrumenting sun/util/resources/cldr/provider/CLDRLocaleDataMetaInfo.

# Created at 2025-07-18T12:34:50.224
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:94)

# Created at 2025-07-18T12:34:50.224
	at java.instrument/java.lang.instrument.ClassFileTransformer.transform(ClassFileTransformer.java:257)

# Created at 2025-07-18T12:34:50.224
	at java.instrument/sun.instrument.TransformerManager.transform(TransformerManager.java:188)

# Created at 2025-07-18T12:34:50.224
	at java.instrument/sun.instrument.InstrumentationImpl.transform(InstrumentationImpl.java:594)

# Created at 2025-07-18T12:34:50.224
	at java.base/java.lang.ClassLoader.defineClass2(Native Method)

# Created at 2025-07-18T12:34:50.225
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1052)

# Created at 2025-07-18T12:34:50.225
	at java.base/java.security.SecureClassLoader.defineClass(SecureClassLoader.java:164)

# Created at 2025-07-18T12:34:50.225
	at java.base/jdk.internal.loader.BuiltinClassLoader.defineClass(BuiltinClassLoader.java:735)

# Created at 2025-07-18T12:34:50.225
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClassInModuleOrNull(BuiltinClassLoader.java:678)

# Created at 2025-07-18T12:34:50.225
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClass(BuiltinClassLoader.java:560)

# Created at 2025-07-18T12:34:50.225
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:602)

# Created at 2025-07-18T12:34:50.225
	at java.base/java.lang.Class.forName(Class.java:595)

# Created at 2025-07-18T12:34:50.225
	at java.base/java.util.ServiceLoader.loadProvider(ServiceLoader.java:755)

# Created at 2025-07-18T12:34:50.225
	at java.base/java.util.ServiceLoader$ModuleServicesLookupIterator.hasNext(ServiceLoader.java:955)

# Created at 2025-07-18T12:34:50.225
	at java.base/java.util.ServiceLoader$1.hasNext(ServiceLoader.java:1164)

# Created at 2025-07-18T12:34:50.226
	at java.base/java.util.ServiceLoader$2.hasNext(ServiceLoader.java:1246)

# Created at 2025-07-18T12:34:50.226
	at java.base/sun.util.cldr.CLDRLocaleProviderAdapter.<init>(CLDRLocaleProviderAdapter.java:75)

# Created at 2025-07-18T12:34:50.226
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)

# Created at 2025-07-18T12:34:50.226
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)

# Created at 2025-07-18T12:34:50.226
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:483)

# Created at 2025-07-18T12:34:50.226
	at java.base/sun.util.locale.provider.LocaleProviderAdapter.forType(LocaleProviderAdapter.java:181)

# Created at 2025-07-18T12:34:50.226
	at java.base/sun.util.locale.provider.LocaleProviderAdapter.findAdapter(LocaleProviderAdapter.java:280)

# Created at 2025-07-18T12:34:50.226
	at java.base/sun.util.locale.provider.LocaleProviderAdapter.getAdapter(LocaleProviderAdapter.java:251)

# Created at 2025-07-18T12:34:50.226
	at java.base/java.text.NumberFormat.getInstance(NumberFormat.java:1105)

# Created at 2025-07-18T12:34:50.227
	at java.base/java.text.NumberFormat.getNumberInstance(NumberFormat.java:624)

# Created at 2025-07-18T12:34:50.227
	at java.base/java.util.Scanner.useLocale(Scanner.java:1295)

# Created at 2025-07-18T12:34:50.227
	at java.base/java.util.Scanner.<init>(Scanner.java:564)

# Created at 2025-07-18T12:34:50.227
	at java.base/java.util.Scanner.<init>(Scanner.java:617)

# Created at 2025-07-18T12:34:50.227
	at java.base/java.util.Scanner.<init>(Scanner.java:603)

# Created at 2025-07-18T12:34:50.227
	at org.apache.maven.surefire.booter.PpidChecker$ProcessInfoConsumer.execute(PpidChecker.java:353)

# Created at 2025-07-18T12:34:50.227
	at org.apache.maven.surefire.booter.PpidChecker.unix(PpidChecker.java:190)

# Created at 2025-07-18T12:34:50.227
	at org.apache.maven.surefire.booter.PpidChecker.isProcessAlive(PpidChecker.java:123)

# Created at 2025-07-18T12:34:50.228
	at org.apache.maven.surefire.booter.ForkedBooter$2.run(ForkedBooter.java:214)

# Created at 2025-07-18T12:34:50.228
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:545)

# Created at 2025-07-18T12:34:50.228
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:369)

# Created at 2025-07-18T12:34:50.228
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:310)

# Created at 2025-07-18T12:34:50.228
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095)

# Created at 2025-07-18T12:34:50.228
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619)

# Created at 2025-07-18T12:34:50.228
	at java.base/java.lang.Thread.run(Thread.java:1447)

# Created at 2025-07-18T12:34:50.228
Caused by: java.io.IOException: Error while instrumenting sun/util/resources/cldr/provider/CLDRLocaleDataMetaInfo.

# Created at 2025-07-18T12:34:50.229
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrumentError(Instrumenter.java:159)

# Created at 2025-07-18T12:34:50.229
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:109)

# Created at 2025-07-18T12:34:50.229
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:92)

# Created at 2025-07-18T12:34:50.229
	... 38 more

# Created at 2025-07-18T12:34:50.229
Caused by: java.lang.IllegalArgumentException: Unsupported class file major version 68

# Created at 2025-07-18T12:34:50.229
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:195)

# Created at 2025-07-18T12:34:50.229
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:176)

# Created at 2025-07-18T12:34:50.230
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:162)

# Created at 2025-07-18T12:34:50.230
	at org.jacoco.agent.rt.internal_43f5073.core.internal.instr.InstrSupport.classReaderFor(InstrSupport.java:280)

# Created at 2025-07-18T12:34:50.230
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:75)

# Created at 2025-07-18T12:34:50.231
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:107)

# Created at 2025-07-18T12:34:50.231
	... 39 more

# Created at 2025-07-18T12:34:50.231
java.lang.instrument.IllegalClassFormatException: Error while instrumenting sun/util/resources/provider/NonBaseLocaleDataMetaInfo.

# Created at 2025-07-18T12:34:50.231
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:94)

# Created at 2025-07-18T12:34:50.231
	at java.instrument/java.lang.instrument.ClassFileTransformer.transform(ClassFileTransformer.java:257)

# Created at 2025-07-18T12:34:50.231
	at java.instrument/sun.instrument.TransformerManager.transform(TransformerManager.java:188)

# Created at 2025-07-18T12:34:50.232
	at java.instrument/sun.instrument.InstrumentationImpl.transform(InstrumentationImpl.java:594)

# Created at 2025-07-18T12:34:50.232
	at java.base/java.lang.ClassLoader.defineClass2(Native Method)

# Created at 2025-07-18T12:34:50.232
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1052)

# Created at 2025-07-18T12:34:50.232
	at java.base/java.security.SecureClassLoader.defineClass(SecureClassLoader.java:164)

# Created at 2025-07-18T12:34:50.232
	at java.base/jdk.internal.loader.BuiltinClassLoader.defineClass(BuiltinClassLoader.java:735)

# Created at 2025-07-18T12:34:50.232
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClassInModuleOrNull(BuiltinClassLoader.java:678)

# Created at 2025-07-18T12:34:50.232
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClass(BuiltinClassLoader.java:560)

# Created at 2025-07-18T12:34:50.232
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:602)

# Created at 2025-07-18T12:34:50.233
	at java.base/java.lang.Class.forName(Class.java:595)

# Created at 2025-07-18T12:34:50.233
	at java.base/java.util.ServiceLoader.loadProvider(ServiceLoader.java:755)

# Created at 2025-07-18T12:34:50.234
	at java.base/java.util.ServiceLoader$ModuleServicesLookupIterator.hasNext(ServiceLoader.java:955)

# Created at 2025-07-18T12:34:50.234
	at java.base/java.util.ServiceLoader$1.hasNext(ServiceLoader.java:1164)

# Created at 2025-07-18T12:34:50.234
	at java.base/java.util.ServiceLoader$2.hasNext(ServiceLoader.java:1246)

# Created at 2025-07-18T12:34:50.235
	at java.base/sun.util.locale.provider.JRELocaleProviderAdapter.createSupportedLocaleString(JRELocaleProviderAdapter.java:423)

# Created at 2025-07-18T12:34:50.235
	at java.base/sun.util.locale.provider.JRELocaleProviderAdapter.createLanguageTagSet(JRELocaleProviderAdapter.java:410)

# Created at 2025-07-18T12:34:50.235
	at java.base/sun.util.locale.provider.FallbackLocaleProviderAdapter.createLanguageTagSet(FallbackLocaleProviderAdapter.java:67)

# Created at 2025-07-18T12:34:50.236
	at java.base/sun.util.locale.provider.JRELocaleProviderAdapter.getLanguageTagSet(JRELocaleProviderAdapter.java:400)

# Created at 2025-07-18T12:34:50.236
	at java.base/sun.util.locale.provider.JRELocaleProviderAdapter.getNumberFormatProvider(JRELocaleProviderAdapter.java:220)

# Created at 2025-07-18T12:34:50.236
	at java.base/sun.util.locale.provider.JRELocaleProviderAdapter.getLocaleServiceProvider(JRELocaleProviderAdapter.java:96)

# Created at 2025-07-18T12:34:50.236
	at java.base/sun.util.locale.provider.LocaleProviderAdapter.findAdapter(LocaleProviderAdapter.java:282)

# Created at 2025-07-18T12:34:50.236
	at java.base/sun.util.locale.provider.LocaleProviderAdapter.getAdapter(LocaleProviderAdapter.java:251)

# Created at 2025-07-18T12:34:50.236
	at java.base/java.text.NumberFormat.getInstance(NumberFormat.java:1105)

# Created at 2025-07-18T12:34:50.236
	at java.base/java.text.NumberFormat.getNumberInstance(NumberFormat.java:624)

# Created at 2025-07-18T12:34:50.236
	at java.base/java.util.Scanner.useLocale(Scanner.java:1295)

# Created at 2025-07-18T12:34:50.236
	at java.base/java.util.Scanner.<init>(Scanner.java:564)

# Created at 2025-07-18T12:34:50.237
	at java.base/java.util.Scanner.<init>(Scanner.java:617)

# Created at 2025-07-18T12:34:50.238
	at java.base/java.util.Scanner.<init>(Scanner.java:603)

# Created at 2025-07-18T12:34:50.238
	at org.apache.maven.surefire.booter.PpidChecker$ProcessInfoConsumer.execute(PpidChecker.java:353)

# Created at 2025-07-18T12:34:50.239
	at org.apache.maven.surefire.booter.PpidChecker.unix(PpidChecker.java:190)

# Created at 2025-07-18T12:34:50.239
	at org.apache.maven.surefire.booter.PpidChecker.isProcessAlive(PpidChecker.java:123)

# Created at 2025-07-18T12:34:50.239
	at org.apache.maven.surefire.booter.ForkedBooter$2.run(ForkedBooter.java:214)

# Created at 2025-07-18T12:34:50.239
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:545)

# Created at 2025-07-18T12:34:50.239
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:369)

# Created at 2025-07-18T12:34:50.240
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:310)

# Created at 2025-07-18T12:34:50.240
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095)

# Created at 2025-07-18T12:34:50.240
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619)

# Created at 2025-07-18T12:34:50.240
	at java.base/java.lang.Thread.run(Thread.java:1447)

# Created at 2025-07-18T12:34:50.241
Caused by: java.io.IOException: Error while instrumenting sun/util/resources/provider/NonBaseLocaleDataMetaInfo.

# Created at 2025-07-18T12:34:50.241
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrumentError(Instrumenter.java:159)

# Created at 2025-07-18T12:34:50.241
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:109)

# Created at 2025-07-18T12:34:50.241
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:92)

# Created at 2025-07-18T12:34:50.244
	... 39 more

# Created at 2025-07-18T12:34:50.244
Caused by: java.lang.IllegalArgumentException: Unsupported class file major version 68

# Created at 2025-07-18T12:34:50.244
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:195)

# Created at 2025-07-18T12:34:50.244
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:176)

# Created at 2025-07-18T12:34:50.244
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:162)

# Created at 2025-07-18T12:34:50.244
	at org.jacoco.agent.rt.internal_43f5073.core.internal.instr.InstrSupport.classReaderFor(InstrSupport.java:280)

# Created at 2025-07-18T12:34:50.244
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:75)

# Created at 2025-07-18T12:34:50.244
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:107)

# Created at 2025-07-18T12:34:50.244
	... 40 more

# Created at 2025-07-18T12:34:50.245
java.lang.instrument.IllegalClassFormatException: Error while instrumenting sun/util/resources/provider/LocaleDataProvider.

# Created at 2025-07-18T12:34:50.245
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:94)

# Created at 2025-07-18T12:34:50.245
	at java.instrument/java.lang.instrument.ClassFileTransformer.transform(ClassFileTransformer.java:257)

# Created at 2025-07-18T12:34:50.245
	at java.instrument/sun.instrument.TransformerManager.transform(TransformerManager.java:188)

# Created at 2025-07-18T12:34:50.246
	at java.instrument/sun.instrument.InstrumentationImpl.transform(InstrumentationImpl.java:594)

# Created at 2025-07-18T12:34:50.246
	at java.base/java.lang.ClassLoader.defineClass2(Native Method)

# Created at 2025-07-18T12:34:50.246
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1052)

# Created at 2025-07-18T12:34:50.246
	at java.base/java.security.SecureClassLoader.defineClass(SecureClassLoader.java:164)

# Created at 2025-07-18T12:34:50.246
	at java.base/jdk.internal.loader.BuiltinClassLoader.defineClass(BuiltinClassLoader.java:735)

# Created at 2025-07-18T12:34:50.246
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClassInModuleOrNull(BuiltinClassLoader.java:678)

# Created at 2025-07-18T12:34:50.246
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClass(BuiltinClassLoader.java:560)

# Created at 2025-07-18T12:34:50.246
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:602)

# Created at 2025-07-18T12:34:50.246
	at java.base/java.lang.Class.forName(Class.java:595)

# Created at 2025-07-18T12:34:50.246
	at java.base/java.util.ServiceLoader.loadProvider(ServiceLoader.java:755)

# Created at 2025-07-18T12:34:50.246
	at java.base/java.util.ServiceLoader$ModuleServicesLookupIterator.hasNext(ServiceLoader.java:955)

# Created at 2025-07-18T12:34:50.246
	at java.base/java.util.ServiceLoader$1.hasNext(ServiceLoader.java:1164)

# Created at 2025-07-18T12:34:50.246
	at java.base/java.util.ServiceLoader$2.hasNext(ServiceLoader.java:1246)

# Created at 2025-07-18T12:34:50.246
	at java.base/sun.util.resources.Bundles.loadBundleFromProviders(Bundles.java:261)

# Created at 2025-07-18T12:34:50.247
	at java.base/sun.util.resources.Bundles.findBundleOf(Bundles.java:199)

# Created at 2025-07-18T12:34:50.247
	at java.base/sun.util.resources.Bundles.findBundleOf(Bundles.java:158)

# Created at 2025-07-18T12:34:50.247
	at java.base/sun.util.resources.Bundles.loadBundleOf(Bundles.java:143)

# Created at 2025-07-18T12:34:50.247
	at java.base/sun.util.resources.Bundles.of(Bundles.java:104)

# Created at 2025-07-18T12:34:50.247
	at java.base/sun.util.resources.LocaleData.getBundle(LocaleData.java:179)

# Created at 2025-07-18T12:34:50.248
	at java.base/sun.util.resources.LocaleData.getNumberFormatData(LocaleData.java:175)

# Created at 2025-07-18T12:34:50.248
	at java.base/sun.util.locale.provider.LocaleResources.getNumberPatterns(LocaleResources.java:543)

# Created at 2025-07-18T12:34:50.248
	at java.base/sun.util.locale.provider.NumberFormatProviderImpl.getInstance(NumberFormatProviderImpl.java:184)

# Created at 2025-07-18T12:34:50.248
	at java.base/sun.util.locale.provider.NumberFormatProviderImpl.getNumberInstance(NumberFormatProviderImpl.java:151)

# Created at 2025-07-18T12:34:50.248
	at java.base/java.text.NumberFormat.getInstance(NumberFormat.java:1121)

# Created at 2025-07-18T12:34:50.248
	at java.base/java.text.NumberFormat.getInstance(NumberFormat.java:1107)

# Created at 2025-07-18T12:34:50.248
	at java.base/java.text.NumberFormat.getNumberInstance(NumberFormat.java:624)

# Created at 2025-07-18T12:34:50.248
	at java.base/java.util.Scanner.useLocale(Scanner.java:1295)

# Created at 2025-07-18T12:34:50.249
	at java.base/java.util.Scanner.<init>(Scanner.java:564)

# Created at 2025-07-18T12:34:50.249
	at java.base/java.util.Scanner.<init>(Scanner.java:617)

# Created at 2025-07-18T12:34:50.249
	at java.base/java.util.Scanner.<init>(Scanner.java:603)

# Created at 2025-07-18T12:34:50.249
	at org.apache.maven.surefire.booter.PpidChecker$ProcessInfoConsumer.execute(PpidChecker.java:353)

# Created at 2025-07-18T12:34:50.249
	at org.apache.maven.surefire.booter.PpidChecker.unix(PpidChecker.java:190)

# Created at 2025-07-18T12:34:50.250
	at org.apache.maven.surefire.booter.PpidChecker.isProcessAlive(PpidChecker.java:123)

# Created at 2025-07-18T12:34:50.250
	at org.apache.maven.surefire.booter.ForkedBooter$2.run(ForkedBooter.java:214)

# Created at 2025-07-18T12:34:50.250
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:545)

# Created at 2025-07-18T12:34:50.250
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:369)

# Created at 2025-07-18T12:34:50.250
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:310)

# Created at 2025-07-18T12:34:50.251
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095)

# Created at 2025-07-18T12:34:50.251
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619)

# Created at 2025-07-18T12:34:50.251
	at java.base/java.lang.Thread.run(Thread.java:1447)

# Created at 2025-07-18T12:34:50.251
Caused by: java.io.IOException: Error while instrumenting sun/util/resources/provider/LocaleDataProvider.

# Created at 2025-07-18T12:34:50.251
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrumentError(Instrumenter.java:159)

# Created at 2025-07-18T12:34:50.251
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:109)

# Created at 2025-07-18T12:34:50.251
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:92)

# Created at 2025-07-18T12:34:50.251
	... 42 more

# Created at 2025-07-18T12:34:50.251
Caused by: java.lang.IllegalArgumentException: Unsupported class file major version 68

# Created at 2025-07-18T12:34:50.252
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:195)

# Created at 2025-07-18T12:34:50.252
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:176)

# Created at 2025-07-18T12:34:50.252
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:162)

# Created at 2025-07-18T12:34:50.252
	at org.jacoco.agent.rt.internal_43f5073.core.internal.instr.InstrSupport.classReaderFor(InstrSupport.java:280)

# Created at 2025-07-18T12:34:50.252
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:75)

# Created at 2025-07-18T12:34:50.252
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:107)

# Created at 2025-07-18T12:34:50.252
	... 43 more

# Created at 2025-07-18T12:34:50.252
java.lang.instrument.IllegalClassFormatException: Error while instrumenting sun/text/resources/cldr/ext/FormatData_zh.

# Created at 2025-07-18T12:34:50.252
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:94)

# Created at 2025-07-18T12:34:50.252
	at java.instrument/java.lang.instrument.ClassFileTransformer.transform(ClassFileTransformer.java:257)

# Created at 2025-07-18T12:34:50.252
	at java.instrument/sun.instrument.TransformerManager.transform(TransformerManager.java:188)

# Created at 2025-07-18T12:34:50.253
	at java.instrument/sun.instrument.InstrumentationImpl.transform(InstrumentationImpl.java:594)

# Created at 2025-07-18T12:34:50.253
	at java.base/java.lang.ClassLoader.defineClass2(Native Method)

# Created at 2025-07-18T12:34:50.253
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1052)

# Created at 2025-07-18T12:34:50.253
	at java.base/java.security.SecureClassLoader.defineClass(SecureClassLoader.java:164)

# Created at 2025-07-18T12:34:50.253
	at java.base/jdk.internal.loader.BuiltinClassLoader.defineClass(BuiltinClassLoader.java:735)

# Created at 2025-07-18T12:34:50.253
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClassInModuleOrNull(BuiltinClassLoader.java:678)

# Created at 2025-07-18T12:34:50.253
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClass(BuiltinClassLoader.java:560)

# Created at 2025-07-18T12:34:50.253
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:602)

# Created at 2025-07-18T12:34:50.253
	at java.base/java.lang.Class.forName(Class.java:595)

# Created at 2025-07-18T12:34:50.254
	at jdk.localedata/sun.util.resources.provider.LocaleDataProvider.loadResourceBundle(LocaleDataProvider.java:53)

# Created at 2025-07-18T12:34:50.254
	at jdk.localedata/sun.util.resources.provider.LocaleDataProvider.getBundle(LocaleDataProvider.java:39)

# Created at 2025-07-18T12:34:50.254
	at java.base/sun.util.resources.Bundles.loadBundleFromProviders(Bundles.java:264)

# Created at 2025-07-18T12:34:50.255
	at java.base/sun.util.resources.Bundles.findBundleOf(Bundles.java:199)

# Created at 2025-07-18T12:34:50.255
	at java.base/sun.util.resources.Bundles.findBundleOf(Bundles.java:158)

# Created at 2025-07-18T12:34:50.256
	at java.base/sun.util.resources.Bundles.loadBundleOf(Bundles.java:143)

# Created at 2025-07-18T12:34:50.256
	at java.base/sun.util.resources.Bundles.of(Bundles.java:104)

# Created at 2025-07-18T12:34:50.256
	at java.base/sun.util.resources.LocaleData.getBundle(LocaleData.java:179)

# Created at 2025-07-18T12:34:50.256
	at java.base/sun.util.resources.LocaleData.getNumberFormatData(LocaleData.java:175)

# Created at 2025-07-18T12:34:50.257
	at java.base/sun.util.locale.provider.LocaleResources.getNumberPatterns(LocaleResources.java:543)

# Created at 2025-07-18T12:34:50.257
	at java.base/sun.util.locale.provider.NumberFormatProviderImpl.getInstance(NumberFormatProviderImpl.java:184)

# Created at 2025-07-18T12:34:50.257
	at java.base/sun.util.locale.provider.NumberFormatProviderImpl.getNumberInstance(NumberFormatProviderImpl.java:151)

# Created at 2025-07-18T12:34:50.257
	at java.base/java.text.NumberFormat.getInstance(NumberFormat.java:1121)

# Created at 2025-07-18T12:34:50.258
	at java.base/java.text.NumberFormat.getInstance(NumberFormat.java:1107)

# Created at 2025-07-18T12:34:50.258
	at java.base/java.text.NumberFormat.getNumberInstance(NumberFormat.java:624)

# Created at 2025-07-18T12:34:50.258
	at java.base/java.util.Scanner.useLocale(Scanner.java:1295)

# Created at 2025-07-18T12:34:50.258
	at java.base/java.util.Scanner.<init>(Scanner.java:564)

# Created at 2025-07-18T12:34:50.258
	at java.base/java.util.Scanner.<init>(Scanner.java:617)

# Created at 2025-07-18T12:34:50.258
	at java.base/java.util.Scanner.<init>(Scanner.java:603)

# Created at 2025-07-18T12:34:50.259
	at org.apache.maven.surefire.booter.PpidChecker$ProcessInfoConsumer.execute(PpidChecker.java:353)

# Created at 2025-07-18T12:34:50.259
	at org.apache.maven.surefire.booter.PpidChecker.unix(PpidChecker.java:190)

# Created at 2025-07-18T12:34:50.259
	at org.apache.maven.surefire.booter.PpidChecker.isProcessAlive(PpidChecker.java:123)

# Created at 2025-07-18T12:34:50.259
	at org.apache.maven.surefire.booter.ForkedBooter$2.run(ForkedBooter.java:214)

# Created at 2025-07-18T12:34:50.259
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:545)

# Created at 2025-07-18T12:34:50.259
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:369)

# Created at 2025-07-18T12:34:50.260
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:310)

# Created at 2025-07-18T12:34:50.260
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1095)

# Created at 2025-07-18T12:34:50.260
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:619)

# Created at 2025-07-18T12:34:50.260
	at java.base/java.lang.Thread.run(Thread.java:1447)

# Created at 2025-07-18T12:34:50.260
Caused by: java.io.IOException: Error while instrumenting sun/text/resources/cldr/ext/FormatData_zh.

# Created at 2025-07-18T12:34:50.260
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrumentError(Instrumenter.java:159)

# Created at 2025-07-18T12:34:50.260
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:109)

# Created at 2025-07-18T12:34:50.260
	at org.jacoco.agent.rt.internal_43f5073.CoverageTransformer.transform(CoverageTransformer.java:92)

# Created at 2025-07-18T12:34:50.260
	... 40 more

# Created at 2025-07-18T12:34:50.260
Caused by: java.lang.IllegalArgumentException: Unsupported class file major version 68

# Created at 2025-07-18T12:34:50.260
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:195)

# Created at 2025-07-18T12:34:50.260
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:176)

# Created at 2025-07-18T12:34:50.261
	at org.jacoco.agent.rt.internal_43f5073.asm.ClassReader.<init>(ClassReader.java:162)

# Created at 2025-07-18T12:34:50.261
	at org.jacoco.agent.rt.internal_43f5073.core.internal.instr.InstrSupport.classReaderFor(InstrSupport.java:280)

# Created at 2025-07-18T12:34:50.261
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:75)

# Created at 2025-07-18T12:34:50.261
	at org.jacoco.agent.rt.internal_43f5073.core.instr.Instrumenter.instrument(Instrumenter.java:107)

# Created at 2025-07-18T12:34:50.261
	... 41 more

# Created at 2025-07-18T12:34:57.241
WARNING: A terminally deprecated method in sun.misc.Unsafe has been called

# Created at 2025-07-18T12:34:57.241
WARNING: sun.misc.Unsafe::objectFieldOffset has been called by io.netty.util.internal.PlatformDependent0$4 (file:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.97.Final/netty-common-4.1.97.Final.jar)

# Created at 2025-07-18T12:34:57.241
WARNING: Please consider reporting this to the maintainers of class io.netty.util.internal.PlatformDependent0$4

# Created at 2025-07-18T12:34:57.242
WARNING: sun.misc.Unsafe::objectFieldOffset will be removed in a future release

# Created at 2025-07-18T12:34:57.262
WARNING: A restricted method in java.lang.System has been called

# Created at 2025-07-18T12:34:57.262
WARNING: java.lang.System::loadLibrary has been called by io.netty.util.internal.NativeLibraryUtil in an unnamed module (file:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.97.Final/netty-common-4.1.97.Final.jar)

# Created at 2025-07-18T12:34:57.262
WARNING: Use --enable-native-access=ALL-UNNAMED to avoid a warning for callers in this module

# Created at 2025-07-18T12:34:57.262
WARNING: Restricted methods will be blocked in a future release unless native access is enabled

# Created at 2025-07-18T12:34:57.262


