-------------------------------------------------------------------------------
Test set: com.sjtu.secondhand.service.impl.RatingServiceImplTest
-------------------------------------------------------------------------------
Tests run: 18, Failures: 1, Errors: 7, Skipped: 0, Time elapsed: 0.037 s <<< FAILURE! - in com.sjtu.secondhand.service.impl.RatingServiceImplTest
createRating_DifferentScores  Time elapsed: 0 s  <<< ERROR!
java.lang.NullPointerException: Cannot invoke "com.sjtu.secondhand.service.NotificationService.createRatingReceivedNotification(java.lang.Long, com.sjtu.secondhand.model.User, com.sjtu.secondhand.model.User)" because "this.notificationService" is null
	at com.sjtu.secondhand.service.impl.RatingServiceImplTest.createRating_DifferentScores(RatingServiceImplTest.java:368)

createRating_UserNotInvolvedInOrder  Time elapsed: 0 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <您不能评价此订单> but was: <您不是该订单的买家或卖家>
	at com.sjtu.secondhand.service.impl.RatingServiceImplTest.createRating_UserNotInvolvedInOrder(RatingServiceImplTest.java:175)

getRatingById_Success  Time elapsed: 0 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.service.impl.RatingServiceImplTest.getRatingById_Success(RatingServiceImplTest.java:181)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

getUserRatings_Success  Time elapsed: 0.006 s  <<< ERROR!
com.sjtu.secondhand.exception.ApiException: 用户不存在
	at com.sjtu.secondhand.service.impl.RatingServiceImplTest.getUserRatings_Success(RatingServiceImplTest.java:248)

getPendingOrderRatings_Success  Time elapsed: 0.002 s  <<< ERROR!
java.lang.NullPointerException: Cannot invoke "com.sjtu.secondhand.model.User.getId()" because the return value of "com.sjtu.secondhand.model.Rating.getRater()" is null
	at com.sjtu.secondhand.service.impl.RatingServiceImplTest.getPendingOrderRatings_Success(RatingServiceImplTest.java:302)

createRating_Success  Time elapsed: 0.001 s  <<< ERROR!
java.lang.NullPointerException: Cannot invoke "com.sjtu.secondhand.service.NotificationService.createRatingReceivedNotification(java.lang.Long, com.sjtu.secondhand.model.User, com.sjtu.secondhand.model.User)" because "this.notificationService" is null
	at com.sjtu.secondhand.service.impl.RatingServiceImplTest.createRating_Success(RatingServiceImplTest.java:98)

getRatingById_NotFound  Time elapsed: 0.001 s  <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.sjtu.secondhand.service.impl.RatingServiceImplTest.getRatingById_NotFound(RatingServiceImplTest.java:195)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.

createRating_WantedTransactionType  Time elapsed: 0.003 s  <<< ERROR!
java.lang.NullPointerException: Cannot invoke "com.sjtu.secondhand.service.NotificationService.createRatingReceivedNotification(java.lang.Long, com.sjtu.secondhand.model.User, com.sjtu.secondhand.model.User)" because "this.notificationService" is null
	at com.sjtu.secondhand.service.impl.RatingServiceImplTest.createRating_WantedTransactionType(RatingServiceImplTest.java:349)

