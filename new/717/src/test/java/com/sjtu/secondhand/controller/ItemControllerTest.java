package com.sjtu.secondhand.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sjtu.secondhand.dto.request.ItemRequest;
import com.sjtu.secondhand.dto.response.ItemPageResponse;
import com.sjtu.secondhand.dto.response.ItemResponse;
import com.sjtu.secondhand.model.Item;
import com.sjtu.secondhand.model.User;
import com.sjtu.secondhand.model.Comment;
import com.sjtu.secondhand.repository.CommentRepository;
import com.sjtu.secondhand.repository.ItemRepository;
import com.sjtu.secondhand.repository.UserRepository;
import com.sjtu.secondhand.service.ItemService;
import com.sjtu.secondhand.service.ElasticsearchSyncService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class ItemControllerTest {

    private MockMvc mockMvc;

    @Mock
    private ItemService itemService;

    @Mock
    private UserRepository userRepository;

    @Mock
    private ItemRepository itemRepository;

    @Mock
    private CommentRepository commentRepository;

    @Mock
    private ElasticsearchSyncService elasticsearchSyncService;

    @Mock
    private SecurityContext securityContext;

    @Mock
    private Authentication authentication;

    @Mock
    private UserDetails userDetails;

    @InjectMocks
    private ItemController itemController;

    private ObjectMapper objectMapper;
    private User testUser;
    private Item testItem;
    private ItemResponse testItemResponse;
    private ItemRequest testItemRequest;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(itemController).build();
        objectMapper = new ObjectMapper();

        // 设置测试用户
        testUser = new User();
        testUser.setId(1L);
        testUser.setUsername("testuser");
        testUser.setEmail("<EMAIL>");

        // 设置测试物品
        testItem = new Item();
        testItem.setId(1L);
        testItem.setName("Test Item");
        testItem.setDescription("Test Description");
        testItem.setPrice(BigDecimal.valueOf(100.0));
        testItem.setUser(testUser);
        testItem.setItemType(Item.ItemType.IDLE);
        testItem.setCondition(Item.ItemCondition.BRAND_NEW);
        testItem.setIsVisible(true);
        testItem.setCreatedAt(LocalDateTime.now());

        // 设置测试ItemResponse
        testItemResponse = new ItemResponse(testItem);

        // 设置测试ItemRequest
        testItemRequest = new ItemRequest();
        testItemRequest.setName("Test Item");
        testItemRequest.setDescription("Test Description");
        testItemRequest.setPrice(BigDecimal.valueOf(100.0));
        testItemRequest.setCategory_id(1);
        testItemRequest.setItem_type("IDLE");
        testItemRequest.setCondition("BRAND_NEW");

        // 模拟安全上下文
        SecurityContextHolder.setContext(securityContext);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        when(authentication.getName()).thenReturn("testuser");
        when(authentication.getPrincipal()).thenReturn(userDetails);
        when(userDetails.getUsername()).thenReturn("testuser");
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
    }

    @Test
    void getItems_shouldReturnItemsList() throws Exception {
        // Arrange
        List<Item> items = Arrays.asList(testItem);
        Page<Item> itemPage = new PageImpl<>(items);
        List<ItemResponse> itemResponses = Arrays.asList(testItemResponse);

        when(itemService.getFilteredItems(anyString(), anyString(), any(Double.class), any(Double.class), 
                anyString(), any(Long.class), any())).thenReturn(itemPage);
        when(itemService.convertItemsWithFavoriteStatus(items)).thenReturn(itemResponses);

        // Act & Assert
        mockMvc.perform(get("/items")
                .param("page", "1")
                .param("size", "20")
                .param("sort", "latest"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("操作成功"))
                .andExpect(jsonPath("$.data").exists());

        verify(itemService).getFilteredItems(anyString(), anyString(), any(Double.class), any(Double.class), 
                anyString(), any(Long.class), any());
        verify(itemService).convertItemsWithFavoriteStatus(items);
    }

    @Test
    void getItems_withException_shouldReturnError() throws Exception {
        // Arrange
        when(itemService.getFilteredItems(anyString(), anyString(), any(Double.class), any(Double.class), 
                anyString(), any(Long.class), any())).thenThrow(new RuntimeException("Service error"));

        // Act & Assert
        mockMvc.perform(get("/items"))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value(org.hamcrest.Matchers.containsString("获取物品列表失败")));
    }

    @Test
    void getItemById_shouldReturnItemDetails() throws Exception {
        // Arrange
        when(itemService.getItemById(1L)).thenReturn(testItemResponse);

        // Act & Assert
        mockMvc.perform(get("/items/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("操作成功"))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.name").value("Test Item"));

        verify(itemService).getItemById(1L);
    }

    @Test
    void createItem_shouldCreateItemSuccessfully() throws Exception {
        // Arrange
        when(itemService.createItem(any())).thenReturn(testItemResponse);

        // Act & Assert
        mockMvc.perform(post("/items")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testItemRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.name").value("Test Item"));

        verify(itemService).createItem(any());
    }

    @Test
    void createItem_withEmptyName_shouldReturnError() throws Exception {
        // Arrange
        testItemRequest.setName("");

        // Act & Assert
        mockMvc.perform(post("/items")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testItemRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("商品名称不能为空"));

        verify(itemService, never()).createItem(any());
    }

    @Test
    void createItem_withNullCategoryId_shouldReturnError() throws Exception {
        // Arrange
        testItemRequest.setCategory_id(null);

        // Act & Assert
        mockMvc.perform(post("/items")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testItemRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("商品分类ID不能为空"));

        verify(itemService, never()).createItem(any());
    }

    @Test
    void createItem_withEmptyCondition_shouldReturnError() throws Exception {
        // Arrange
        testItemRequest.setCondition("");

        // Act & Assert
        mockMvc.perform(post("/items")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testItemRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("商品状态不能为空"));

        verify(itemService, never()).createItem(any());
    }

    @Test
    void createItem_withException_shouldReturnError() throws Exception {
        // Arrange
        when(itemService.createItem(any())).thenThrow(new RuntimeException("Create error"));

        // Act & Assert
        mockMvc.perform(post("/items")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testItemRequest)))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value(org.hamcrest.Matchers.containsString("创建商品失败")));
    }

    @Test
    void updateItem_shouldUpdateItemSuccessfully() throws Exception {
        // Arrange
        when(itemService.updateItem(eq(1L), any())).thenReturn(testItemResponse);

        // Act & Assert
        mockMvc.perform(put("/items/1")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testItemRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("物品更新成功"));

        verify(itemService).updateItem(eq(1L), any());
    }

    @Test
    void updateItemStatus_shouldUpdateStatusSuccessfully() throws Exception {
        // Arrange
        when(itemRepository.findById(1L)).thenReturn(Optional.of(testItem));
        when(itemRepository.save(any())).thenReturn(testItem);

        Map<String, String> statusData = new HashMap<>();
        statusData.put("status", "listed");

        // Act & Assert
        mockMvc.perform(patch("/items/1/status")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(statusData)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("物品状态更新成功"));

        verify(itemRepository).save(testItem);
    }

    @Test
    void updateItemStatus_withInvalidStatus_shouldReturnError() throws Exception {
        // Arrange
        Map<String, String> statusData = new HashMap<>();
        statusData.put("status", "invalid");

        when(itemRepository.findById(1L)).thenReturn(Optional.of(testItem));

        // Act & Assert
        mockMvc.perform(patch("/items/1/status")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(statusData)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value(org.hamcrest.Matchers.containsString("无效的状态值")));
    }

    @Test
    void updateItemStatus_withNonOwner_shouldReturnForbidden() throws Exception {
        // Arrange
        User otherUser = new User();
        otherUser.setId(2L);
        testItem.setUser(otherUser);

        when(itemRepository.findById(1L)).thenReturn(Optional.of(testItem));

        Map<String, String> statusData = new HashMap<>();
        statusData.put("status", "listed");

        // Act & Assert
        mockMvc.perform(patch("/items/1/status")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(statusData)))
                .andExpect(status().isForbidden())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("您没有权限更新此物品"));
    }

    @Test
    void deleteItem_shouldDeleteItemSuccessfully() throws Exception {
        // Arrange
        when(itemRepository.findById(1L)).thenReturn(Optional.of(testItem));

        // Act & Assert
        mockMvc.perform(delete("/items/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("物品删除成功"));

        verify(itemRepository).delete(testItem);
    }

    @Test
    void deleteItem_withNonOwner_shouldReturnForbidden() throws Exception {
        // Arrange
        User otherUser = new User();
        otherUser.setId(2L);
        testItem.setUser(otherUser);

        when(itemRepository.findById(1L)).thenReturn(Optional.of(testItem));

        // Act & Assert
        mockMvc.perform(delete("/items/1"))
                .andExpect(status().isForbidden())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("您没有权限删除此物品"));

        verify(itemRepository, never()).delete(any());
    }

    @Test
    void deleteItem_withNonExistentItem_shouldReturnError() throws Exception {
        // Arrange
        when(itemRepository.findById(1L)).thenReturn(Optional.empty());

        // Act & Assert
        mockMvc.perform(delete("/items/1"))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value(org.hamcrest.Matchers.containsString("删除物品失败")));
    }

    @Test
    void addToFavorites_shouldAddSuccessfully() throws Exception {
        // Arrange
        doNothing().when(itemService).addToFavorites(1L, 1L);
        when(itemService.getItemById(1L)).thenReturn(testItemResponse);

        // Act & Assert
        mockMvc.perform(post("/items/1/favorite"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("收藏成功"));

        verify(itemService).addToFavorites(1L, 1L);
    }

    @Test
    void removeFromFavorites_shouldRemoveSuccessfully() throws Exception {
        // Arrange
        doNothing().when(itemService).removeFromFavorites(1L, 1L);
        when(itemService.getItemById(1L)).thenReturn(testItemResponse);

        // Act & Assert
        mockMvc.perform(delete("/items/1/favorite"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("取消收藏成功"));

        verify(itemService).removeFromFavorites(1L, 1L);
    }

    @Test
    void getItemComments_shouldReturnCommentsList() throws Exception {
        // Arrange
        when(itemRepository.findById(1L)).thenReturn(Optional.of(testItem));

        Comment comment = new Comment();
        comment.setId(1L);
        comment.setContent("Test comment");
        comment.setUser(testUser);
        comment.setItem(testItem);
        comment.setCreatedAt(LocalDateTime.now());

        List<Comment> comments = Arrays.asList(comment);
        when(commentRepository.findByItemAndParentIsNullOrderByCreatedAtDesc(testItem))
                .thenReturn(comments);
        when(commentRepository.findByParentOrderByCreatedAtAsc(comment))
                .thenReturn(Collections.emptyList());

        // Act & Assert
        mockMvc.perform(get("/items/1/comments"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取评论成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].content").value("Test comment"));

        verify(commentRepository).findByItemAndParentIsNullOrderByCreatedAtDesc(testItem);
    }

    @Test
    void getItemComments_withNonExistentItem_shouldReturnError() throws Exception {
        // Arrange
        when(itemRepository.findById(1L)).thenReturn(Optional.empty());

        // Act & Assert
        mockMvc.perform(get("/items/1/comments"))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value(org.hamcrest.Matchers.containsString("获取评论失败")));
    }

    @Test
    void addComment_shouldAddCommentSuccessfully() throws Exception {
        // Arrange
        when(itemRepository.findById(1L)).thenReturn(Optional.of(testItem));

        Comment savedComment = new Comment();
        savedComment.setId(1L);
        savedComment.setContent("New comment");
        savedComment.setUser(testUser);
        savedComment.setItem(testItem);
        savedComment.setCreatedAt(LocalDateTime.now());

        when(commentRepository.save(any())).thenReturn(savedComment);

        Map<String, Object> commentData = new HashMap<>();
        commentData.put("content", "New comment");

        // Act & Assert
        mockMvc.perform(post("/items/1/comments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(commentData)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("评论成功"))
                .andExpect(jsonPath("$.data.content").value("New comment"));

        verify(commentRepository).save(any());
    }

    @Test
    void addComment_withEmptyContent_shouldReturnError() throws Exception {
        // Arrange
        Map<String, Object> commentData = new HashMap<>();
        commentData.put("content", "");

        // Act & Assert
        mockMvc.perform(post("/items/1/comments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(commentData)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("评论内容不能为空"));

        verify(commentRepository, never()).save(any());
    }

    @Test
    void addComment_withParentId_shouldAddReplySuccessfully() throws Exception {
        // Arrange
        when(itemRepository.findById(1L)).thenReturn(Optional.of(testItem));

        Comment parentComment = new Comment();
        parentComment.setId(1L);
        when(commentRepository.findById(1L)).thenReturn(Optional.of(parentComment));

        Comment savedComment = new Comment();
        savedComment.setId(2L);
        savedComment.setContent("Reply comment");
        savedComment.setUser(testUser);
        savedComment.setItem(testItem);
        savedComment.setParent(parentComment);
        savedComment.setCreatedAt(LocalDateTime.now());

        when(commentRepository.save(any())).thenReturn(savedComment);

        Map<String, Object> commentData = new HashMap<>();
        commentData.put("content", "Reply comment");
        commentData.put("parent_id", "1");

        // Act & Assert
        mockMvc.perform(post("/items/1/comments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(commentData)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("评论成功"));

        verify(commentRepository).save(any());
        verify(commentRepository).findById(1L);
    }

    @Test
    void getAllItemsForDebug_shouldReturnAllItems() throws Exception {
        // Arrange
        List<Item> items = Arrays.asList(testItem);
        when(itemRepository.findAll()).thenReturn(items);

        // Act & Assert
        mockMvc.perform(get("/items/debug"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取所有商品成功"))
                .andExpect(jsonPath("$.data").isArray());

        verify(itemRepository).findAll();
    }

    @Test
    void getMyItems_shouldReturnUserItems() throws Exception {
        // Arrange
        List<Item> items = Arrays.asList(testItem);
        when(itemRepository.findByUserId(1L)).thenReturn(items);

        // Act & Assert
        mockMvc.perform(get("/items/my"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取我的物品成功"))
                .andExpect(jsonPath("$.data").isArray());

        verify(itemRepository).findByUserId(1L);
    }

    @Test
    void getMyItems_withItemType_shouldReturnFilteredItems() throws Exception {
        // Arrange
        List<Item> items = Arrays.asList(testItem);
        when(itemRepository.findByUserIdAndItemType(1L, Item.ItemType.IDLE)).thenReturn(items);

        // Act & Assert
        mockMvc.perform(get("/items/my")
                .param("item_type", "IDLE"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray());

        verify(itemRepository).findByUserIdAndItemType(1L, Item.ItemType.IDLE);
    }

    @Test
    void searchWithEs_directEs_shouldReturnEsResults() throws Exception {
        // Arrange
        Map<String, Object> mockResult = new HashMap<>();
        mockResult.put("source", "elasticsearch_direct");
        mockResult.put("hits", Arrays.asList(testItemResponse));

        when(elasticsearchSyncService.directAdvancedSearch(
                eq("test"), eq(1L), eq(50.0), eq(150.0), eq("IDLE"), eq("NEW"), eq(0), eq(10), eq("latest")))
                .thenReturn(mockResult);

        // Act & Assert
        mockMvc.perform(get("/items/search/es")
                .param("q", "test")
                .param("page", "0")
                .param("size", "10")
                .param("category_id", "1")
                .param("price_min", "50.0")
                .param("price_max", "150.0")
                .param("item_type", "IDLE")
                .param("condition", "NEW")
                .param("sort", "latest")
                .param("direct_es", "true"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("搜索成功"));

        verify(elasticsearchSyncService).directAdvancedSearch(
                eq("test"), eq(1L), eq(50.0), eq(150.0), eq("IDLE"), eq("NEW"), eq(0), eq(10), eq("latest"));
    }

    @Test
    void searchWithEs_standardEs_shouldReturnResults() throws Exception {
        // Arrange
        Map<String, Object> mockResult = new HashMap<>();
        mockResult.put("source", "elasticsearch");
        
        when(elasticsearchSyncService.advancedSearch(
                eq("test"), eq(1L), eq(50.0), eq(150.0), eq("IDLE"), eq("NEW"), eq(0), eq(10), eq("latest")))
                .thenReturn(mockResult);

        // Act & Assert
        mockMvc.perform(get("/items/search/es")
                .param("q", "test")
                .param("direct_es", "false"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("搜索成功"));

        verify(elasticsearchSyncService).advancedSearch(
                eq("test"), eq(1L), eq(50.0), eq(150.0), eq("IDLE"), eq("NEW"), eq(0), eq(10), eq("latest"));
    }

    @Test
    void searchWithEs_fallbackToMySQL_shouldReturnMySQLResults() throws Exception {
        // Arrange
        Map<String, Object> mockResult = new HashMap<>();
        mockResult.put("source", "elasticsearch_fallback");
        
        when(elasticsearchSyncService.directAdvancedSearch(any(), any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(mockResult);
        
        List<Item> items = Arrays.asList(testItem);
        Page<Item> itemPage = new PageImpl<>(items);
        List<ItemResponse> itemResponses = Arrays.asList(testItemResponse);
        
        when(itemService.getFilteredItems(any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(itemPage);
        when(itemService.convertItemsWithFavoriteStatus(items)).thenReturn(itemResponses);

        // Act & Assert
        mockMvc.perform(get("/items/search/es")
                .param("q", "test"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("操作成功"));

        verify(itemService).getFilteredItems(any(), any(), any(), any(), any(), any(), any());
    }

    @Test
    void searchWithEs_withException_shouldFallbackToMySQL() throws Exception {
        // Arrange
        when(elasticsearchSyncService.directAdvancedSearch(any(), any(), any(), any(), any(), any(), any(), any(), any()))
                .thenThrow(new RuntimeException("ES error"));
        
        List<Item> items = Arrays.asList(testItem);
        Page<Item> itemPage = new PageImpl<>(items);
        List<ItemResponse> itemResponses = Arrays.asList(testItemResponse);
        
        when(itemService.getFilteredItems(any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(itemPage);
        when(itemService.convertItemsWithFavoriteStatus(items)).thenReturn(itemResponses);

        // Act & Assert
        mockMvc.perform(get("/items/search/es")
                .param("q", "test"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("操作成功"));

        verify(itemService).getFilteredItems(any(), any(), any(), any(), any(), any(), any());
    }

    @Test
    void updateItemStatus_withUnlistedStatus_shouldUpdateSuccessfully() throws Exception {
        // Arrange
        Map<String, String> statusData = new HashMap<>();
        statusData.put("status", "unlisted");

        testItem.setIsVisible(false);
        when(itemRepository.findById(1L)).thenReturn(Optional.of(testItem));
        when(itemRepository.save(any(Item.class))).thenReturn(testItem);

        // Act & Assert
        mockMvc.perform(patch("/items/1/status")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(statusData)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("物品状态更新成功"));

        verify(itemRepository).findById(1L);
        verify(itemRepository).save(any(Item.class));
    }

    @Test
    void updateItemStatus_withEmptyStatus_shouldReturnError() throws Exception {
        // Arrange
        Map<String, String> statusData = new HashMap<>();
        statusData.put("status", "");

        // Act & Assert
        mockMvc.perform(patch("/items/1/status")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(statusData)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("状态不能为空"));

        verify(itemRepository, never()).findById(any());
    }

    @Test
    void updateItemStatus_withNullStatus_shouldReturnError() throws Exception {
        // Arrange
        Map<String, String> statusData = new HashMap<>();
        statusData.put("status", null);

        // Act & Assert
        mockMvc.perform(patch("/items/1/status")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(statusData)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("状态不能为空"));

        verify(itemRepository, never()).findById(any());
    }

    @Test
    void updateItemStatus_withItemNotFound_shouldThrowException() throws Exception {
        // Arrange
        Map<String, String> statusData = new HashMap<>();
        statusData.put("status", "listed");

        when(itemRepository.findById(1L)).thenReturn(Optional.empty());

        // Act & Assert
        mockMvc.perform(patch("/items/1/status")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(statusData)))
                .andExpect(status().isInternalServerError());

        verify(itemRepository).findById(1L);
        verify(itemRepository, never()).save(any());
    }

    @Test
    void deleteItem_shouldDeleteSuccessfully() throws Exception {
        // Arrange
        when(itemRepository.findById(1L)).thenReturn(Optional.of(testItem));
        doNothing().when(itemRepository).delete(testItem);

        // Act & Assert
        mockMvc.perform(delete("/items/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("物品删除成功"));

        verify(itemRepository).findById(1L);
        verify(itemRepository).delete(testItem);
    }

    @Test
    void deleteItem_withItemNotFound_shouldThrowException() throws Exception {
        // Arrange
        when(itemRepository.findById(1L)).thenReturn(Optional.empty());

        // Act & Assert
        mockMvc.perform(delete("/items/1"))
                .andExpect(status().isInternalServerError());

        verify(itemRepository).findById(1L);
        verify(itemRepository, never()).delete(any());
    }

    @Test
    void deleteItem_withUnauthorizedUser_shouldReturnForbidden() throws Exception {
        // Arrange
        User otherUser = new User();
        otherUser.setId(2L);
        otherUser.setUsername("otheruser");
        testItem.setUser(otherUser);

        when(itemRepository.findById(1L)).thenReturn(Optional.of(testItem));

        // Act & Assert
        mockMvc.perform(delete("/items/1"))
                .andExpect(status().isForbidden())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("您没有权限删除此物品"));

        verify(itemRepository).findById(1L);
        verify(itemRepository, never()).delete(any());
    }
}