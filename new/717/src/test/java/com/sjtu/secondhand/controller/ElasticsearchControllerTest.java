package com.sjtu.secondhand.controller;

import com.sjtu.secondhand.service.ElasticsearchSyncService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class ElasticsearchControllerTest {

    @Mock
    private ElasticsearchSyncService elasticsearchSyncService;

    @InjectMocks
    private ElasticsearchController elasticsearchController;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(elasticsearchController).build();
    }

    @Test
    void testSyncAllItems_Success() throws Exception {
        doNothing().when(elasticsearchSyncService).syncAllItemsToElasticsearch();

        mockMvc.perform(post("/es/sync")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("同步所有物品到Elasticsearch成功"));

        verify(elasticsearchSyncService, times(1)).syncAllItemsToElasticsearch();
    }

    @Test
    void testSyncAllItems_ServiceThrowsException() throws Exception {
        doThrow(new RuntimeException("ES连接失败")).when(elasticsearchSyncService).syncAllItemsToElasticsearch();

        mockMvc.perform(post("/es/sync")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value("ES_SYNC_ERROR"));

        verify(elasticsearchSyncService, times(1)).syncAllItemsToElasticsearch();
    }

    @Test
    void testSearch_Success() throws Exception {
        Map<String, Object> searchResult = new HashMap<>();
        searchResult.put("total", 5);
        searchResult.put("items", Arrays.asList(
            createMockItem(1L, "iPhone 13"),
            createMockItem(2L, "iPhone 14")
        ));

        when(elasticsearchSyncService.searchItemsByKeyword(eq("iPhone"), eq(0), eq(10)))
                .thenReturn(searchResult);

        mockMvc.perform(get("/es/search")
                .param("q", "iPhone")
                .param("page", "0")
                .param("size", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.total").value(5))
                .andExpect(jsonPath("$.data.items").isArray());

        verify(elasticsearchSyncService, times(1)).searchItemsByKeyword("iPhone", 0, 10);
    }

    @Test
    void testSearch_WithFallback() throws Exception {
        Map<String, Object> fallbackResult = new HashMap<>();
        fallbackResult.put("source", "elasticsearch_fallback");
        fallbackResult.put("total", 3);
        fallbackResult.put("items", Arrays.asList(createMockItem(1L, "手机")));

        when(elasticsearchSyncService.searchItemsByKeyword(eq("手机"), eq(0), eq(10)))
                .thenReturn(fallbackResult);

        mockMvc.perform(get("/es/search")
                .param("q", "手机")
                .param("page", "0")
                .param("size", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("由于Elasticsearch兼容性问题，已回退到MySQL搜索"));

        verify(elasticsearchSyncService, times(1)).searchItemsByKeyword("手机", 0, 10);
    }

    @Test
    void testSearch_WithError() throws Exception {
        Map<String, Object> errorResult = new HashMap<>();
        errorResult.put("error", "ES查询失败");

        when(elasticsearchSyncService.searchItemsByKeyword(eq("test"), eq(0), eq(10)))
                .thenReturn(errorResult);

        mockMvc.perform(get("/es/search")
                .param("q", "test")
                .param("page", "0")
                .param("size", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value("ES_SEARCH_ERROR"));

        verify(elasticsearchSyncService, times(1)).searchItemsByKeyword("test", 0, 10);
    }

    @Test
    void testSearch_ServiceThrowsException() throws Exception {
        when(elasticsearchSyncService.searchItemsByKeyword(anyString(), anyInt(), anyInt()))
                .thenThrow(new RuntimeException("ES服务异常"));

        mockMvc.perform(get("/es/search")
                .param("q", "test")
                .param("page", "0")
                .param("size", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value("ES_SEARCH_ERROR"));

        verify(elasticsearchSyncService, times(1)).searchItemsByKeyword("test", 0, 10);
    }

    @Test
    void testAdvancedSearch_Success() throws Exception {
        Map<String, Object> searchResult = new HashMap<>();
        searchResult.put("total", 2);
        searchResult.put("items", Arrays.asList(createMockItem(1L, "相机")));

        when(elasticsearchSyncService.advancedSearch(
                eq("相机"), eq(1L), eq(100.0), eq(500.0), 
                eq("sell"), eq("good"), eq(0), eq(10), eq("latest")))
                .thenReturn(searchResult);

        mockMvc.perform(get("/es/search/advanced")
                .param("q", "相机")
                .param("category_id", "1")
                .param("price_min", "100.0")
                .param("price_max", "500.0")
                .param("item_type", "sell")
                .param("condition", "good")
                .param("page", "0")
                .param("size", "10")
                .param("sort", "latest")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.total").value(2));

        verify(elasticsearchSyncService, times(1)).advancedSearch(
                "相机", 1L, 100.0, 500.0, "sell", "good", 0, 10, "latest");
    }

    @Test
    void testAdvancedSearch_WithFallback() throws Exception {
        Map<String, Object> fallbackResult = new HashMap<>();
        fallbackResult.put("source", "elasticsearch_fallback");
        fallbackResult.put("total", 1);

        when(elasticsearchSyncService.advancedSearch(
                any(), any(), any(), any(), any(), any(), anyInt(), anyInt(), any()))
                .thenReturn(fallbackResult);

        mockMvc.perform(get("/es/search/advanced")
                .param("q", "laptop")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("由于Elasticsearch兼容性问题，已回退到MySQL搜索"));

        verify(elasticsearchSyncService, times(1)).advancedSearch(
                "laptop", null, null, null, null, null, 0, 10, null);
    }

    @Test
    void testAdvancedSearch_WithError() throws Exception {
        Map<String, Object> errorResult = new HashMap<>();
        errorResult.put("error", "高级搜索失败");

        when(elasticsearchSyncService.advancedSearch(
                any(), any(), any(), any(), any(), any(), anyInt(), anyInt(), any()))
                .thenReturn(errorResult);

        mockMvc.perform(get("/es/search/advanced")
                .param("q", "error")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value("ES_SEARCH_ERROR"));

        verify(elasticsearchSyncService, times(1)).advancedSearch(
                "error", null, null, null, null, null, 0, 10, null);
    }

    @Test
    void testAdvancedSearch_ServiceThrowsException() throws Exception {
        when(elasticsearchSyncService.advancedSearch(
                any(), any(), any(), any(), any(), any(), anyInt(), anyInt(), any()))
                .thenThrow(new RuntimeException("高级搜索服务异常"));

        mockMvc.perform(get("/es/search/advanced")
                .param("q", "exception")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value("ES_ADVANCED_SEARCH_ERROR"));

        verify(elasticsearchSyncService, times(1)).advancedSearch(
                "exception", null, null, null, null, null, 0, 10, null);
    }

    @Test
    void testDirectSearch_Success() throws Exception {
        Map<String, Object> directResult = new HashMap<>();
        directResult.put("total", 3);
        directResult.put("items", Arrays.asList(
            createMockItem(1L, "手机"),
            createMockItem(2L, "电脑")
        ));

        when(elasticsearchSyncService.directSearchByKeyword(eq("手机"), eq(0), eq(10)))
                .thenReturn(directResult);

        mockMvc.perform(get("/es/direct/search")
                .param("q", "手机")
                .param("page", "0")
                .param("size", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.totalItems").value(3))
                .andExpect(jsonPath("$.data.items").isArray());

        verify(elasticsearchSyncService, times(1)).directSearchByKeyword("手机", 0, 10);
    }

    @Test
    void testDirectSearch_WithError() throws Exception {
        Map<String, Object> errorResult = new HashMap<>();
        errorResult.put("error", "直接搜索失败");

        when(elasticsearchSyncService.directSearchByKeyword(eq("error"), eq(0), eq(10)))
                .thenReturn(errorResult);

        mockMvc.perform(get("/es/direct/search")
                .param("q", "error")
                .param("page", "0")
                .param("size", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value("ES_DIRECT_SEARCH_ERROR"));

        verify(elasticsearchSyncService, times(1)).directSearchByKeyword("error", 0, 10);
    }

    @Test
    void testDirectSearch_ServiceThrowsException() throws Exception {
        when(elasticsearchSyncService.directSearchByKeyword(anyString(), anyInt(), anyInt()))
                .thenThrow(new RuntimeException("直接搜索服务异常"));

        mockMvc.perform(get("/es/direct/search")
                .param("q", "exception")
                .param("page", "0")
                .param("size", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value("ES_DIRECT_SEARCH_ERROR"));

        verify(elasticsearchSyncService, times(1)).directSearchByKeyword("exception", 0, 10);
    }

    @Test
    void testDirectAdvancedSearch_Success() throws Exception {
        Map<String, Object> directResult = new HashMap<>();
        directResult.put("total", 1);
        directResult.put("items", Arrays.asList(createMockItem(1L, "相机")));

        when(elasticsearchSyncService.directAdvancedSearch(
                eq("相机"), eq(1L), eq(100.0), eq(500.0), 
                eq("sell"), eq("good"), eq(0), eq(10), eq("latest")))
                .thenReturn(directResult);

        mockMvc.perform(get("/es/direct/search/advanced")
                .param("q", "相机")
                .param("category_id", "1")
                .param("price_min", "100.0")
                .param("price_max", "500.0")
                .param("item_type", "sell")
                .param("condition", "good")
                .param("page", "0")
                .param("size", "10")
                .param("sort", "latest")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.totalItems").value(1));

        verify(elasticsearchSyncService, times(1)).directAdvancedSearch(
                "相机", 1L, 100.0, 500.0, "sell", "good", 0, 10, "latest");
    }

    @Test
    void testDirectAdvancedSearch_WithError() throws Exception {
        Map<String, Object> errorResult = new HashMap<>();
        errorResult.put("error", "直接高级搜索失败");

        when(elasticsearchSyncService.directAdvancedSearch(
                any(), any(), any(), any(), any(), any(), anyInt(), anyInt(), any()))
                .thenReturn(errorResult);

        mockMvc.perform(get("/es/direct/search/advanced")
                .param("q", "error")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value("ES_DIRECT_ADVANCED_SEARCH_ERROR"));

        verify(elasticsearchSyncService, times(1)).directAdvancedSearch(
                "error", null, null, null, null, null, 0, 10, null);
    }

    @Test
    void testDirectAdvancedSearch_ServiceThrowsException() throws Exception {
        when(elasticsearchSyncService.directAdvancedSearch(
                any(), any(), any(), any(), any(), any(), anyInt(), anyInt(), any()))
                .thenThrow(new RuntimeException("直接高级搜索服务异常"));

        mockMvc.perform(get("/es/direct/search/advanced")
                .param("q", "exception")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value("ES_DIRECT_ADVANCED_SEARCH_ERROR"));

        verify(elasticsearchSyncService, times(1)).directAdvancedSearch(
                "exception", null, null, null, null, null, 0, 10, null);
    }

    @Test
    void testTestDirectSearch_Success() throws Exception {
        Map<String, Object> testResult = new HashMap<>();
        testResult.put("total", 2);
        testResult.put("items", Arrays.asList(
            createMockItem(1L, "手机"),
            createMockItem(2L, "手机壳")
        ));

        when(elasticsearchSyncService.directSearchByKeyword(eq("手机"), eq(0), eq(10)))
                .thenReturn(testResult);

        mockMvc.perform(get("/es/test/direct")
                .param("q", "手机")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.total").value(2));

        verify(elasticsearchSyncService, times(1)).directSearchByKeyword("手机", 0, 10);
    }

    @Test
    void testTestDirectSearch_WithDefaultKeyword() throws Exception {
        Map<String, Object> testResult = new HashMap<>();
        testResult.put("total", 1);
        testResult.put("items", Arrays.asList(createMockItem(1L, "手机")));

        when(elasticsearchSyncService.directSearchByKeyword(eq("手机"), eq(0), eq(10)))
                .thenReturn(testResult);

        mockMvc.perform(get("/es/test/direct")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        verify(elasticsearchSyncService, times(1)).directSearchByKeyword("手机", 0, 10);
    }

    @Test
    void testTestDirectSearch_WithError() throws Exception {
        Map<String, Object> errorResult = new HashMap<>();
        errorResult.put("error", "测试搜索失败");

        when(elasticsearchSyncService.directSearchByKeyword(eq("error"), eq(0), eq(10)))
                .thenReturn(errorResult);

        mockMvc.perform(get("/es/test/direct")
                .param("q", "error")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value("ES_TEST_ERROR"));

        verify(elasticsearchSyncService, times(1)).directSearchByKeyword("error", 0, 10);
    }

    @Test
    void testTestDirectSearch_ServiceThrowsException() throws Exception {
        when(elasticsearchSyncService.directSearchByKeyword(anyString(), anyInt(), anyInt()))
                .thenThrow(new RuntimeException("测试搜索服务异常"));

        mockMvc.perform(get("/es/test/direct")
                .param("q", "exception")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value("ES_TEST_ERROR"));

        verify(elasticsearchSyncService, times(1)).directSearchByKeyword("exception", 0, 10);
    }

    @Test
    void testTestFormattedSearch_Success() throws Exception {
        Map<String, Object> testResult = new HashMap<>();
        testResult.put("total", 1);
        testResult.put("items", Arrays.asList(createMockItem(1L, "手机")));

        when(elasticsearchSyncService.directSearchByKeyword(eq("手机"), eq(0), eq(10)))
                .thenReturn(testResult);

        mockMvc.perform(get("/es/test/format")
                .param("q", "手机")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("格式化后的ES搜索结果"));

        verify(elasticsearchSyncService, times(1)).directSearchByKeyword("手机", 0, 10);
    }

    @Test
    void testTestFormattedSearch_WithDefaultKeyword() throws Exception {
        Map<String, Object> testResult = new HashMap<>();
        testResult.put("total", 1);
        testResult.put("items", Arrays.asList(createMockItem(1L, "手机")));

        when(elasticsearchSyncService.directSearchByKeyword(eq("手机"), eq(0), eq(10)))
                .thenReturn(testResult);

        mockMvc.perform(get("/es/test/format")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        verify(elasticsearchSyncService, times(1)).directSearchByKeyword("手机", 0, 10);
    }

    @Test
    void testTestFormattedSearch_WithError() throws Exception {
        Map<String, Object> errorResult = new HashMap<>();
        errorResult.put("error", "格式化搜索失败");

        when(elasticsearchSyncService.directSearchByKeyword(eq("error"), eq(0), eq(10)))
                .thenReturn(errorResult);

        mockMvc.perform(get("/es/test/format")
                .param("q", "error")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value("ES_TEST_ERROR"));

        verify(elasticsearchSyncService, times(1)).directSearchByKeyword("error", 0, 10);
    }

    @Test
    void testTestFormattedSearch_ServiceThrowsException() throws Exception {
        when(elasticsearchSyncService.directSearchByKeyword(anyString(), anyInt(), anyInt()))
                .thenThrow(new RuntimeException("格式化搜索服务异常"));

        mockMvc.perform(get("/es/test/format")
                .param("q", "exception")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value("ES_TEST_ERROR"));

        verify(elasticsearchSyncService, times(1)).directSearchByKeyword("exception", 0, 10);
    }

    @Test
    void testTestFormattedSearch2_Success() throws Exception {
        Map<String, Object> testResult = new HashMap<>();
        testResult.put("total", 1);
        testResult.put("items", Arrays.asList(createMockItem(1L, "相机")));

        when(elasticsearchSyncService.directSearchByKeyword(eq("相机"), eq(0), eq(10)))
                .thenReturn(testResult);

        mockMvc.perform(get("/es/test/format2")
                .param("q", "相机")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("格式化后的ES搜索结果"));

        verify(elasticsearchSyncService, times(1)).directSearchByKeyword("相机", 0, 10);
    }

    @Test
    void testTestFormattedSearch2_WithDefaultKeyword() throws Exception {
        Map<String, Object> testResult = new HashMap<>();
        testResult.put("total", 1);
        testResult.put("items", Arrays.asList(createMockItem(1L, "相机")));

        when(elasticsearchSyncService.directSearchByKeyword(eq("相机"), eq(0), eq(10)))
                .thenReturn(testResult);

        mockMvc.perform(get("/es/test/format2")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        verify(elasticsearchSyncService, times(1)).directSearchByKeyword("相机", 0, 10);
    }

    @Test
    void testTestFormattedSearch2_WithError() throws Exception {
        Map<String, Object> errorResult = new HashMap<>();
        errorResult.put("error", "格式化搜索2失败");

        when(elasticsearchSyncService.directSearchByKeyword(eq("error"), eq(0), eq(10)))
                .thenReturn(errorResult);

        mockMvc.perform(get("/es/test/format2")
                .param("q", "error")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value("ES_TEST_ERROR"));

        verify(elasticsearchSyncService, times(1)).directSearchByKeyword("error", 0, 10);
    }

    @Test
    void testTestFormattedSearch2_ServiceThrowsException() throws Exception {
        when(elasticsearchSyncService.directSearchByKeyword(anyString(), anyInt(), anyInt()))
                .thenThrow(new RuntimeException("格式化搜索2服务异常"));

        mockMvc.perform(get("/es/test/format2")
                .param("q", "exception")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value("ES_TEST_ERROR"));

        verify(elasticsearchSyncService, times(1)).directSearchByKeyword("exception", 0, 10);
    }

    @Test
    void testConstructor() {
        ElasticsearchSyncService mockService = mock(ElasticsearchSyncService.class);
        ElasticsearchController controller = new ElasticsearchController(mockService);
        assertNotNull(controller);
    }

    private Map<String, Object> createMockItem(Long id, String name) {
        Map<String, Object> item = new HashMap<>();
        item.put("id", id);
        item.put("name", name);
        item.put("price", 100.0);
        item.put("description", "测试商品");
        return item;
    }

    private void assertNotNull(ElasticsearchController controller) {
        if (controller == null) {
            throw new AssertionError("Controller should not be null");
        }
    }

    @Test
    void testSearch_WithNonMapResult() throws Exception {
        // 测试非Map类型的结果
        List<Map<String, Object>> items = Arrays.asList(createMockItem(1L, "测试商品"));
        when(elasticsearchSyncService.searchItemsByKeyword("test", 0, 10)).thenReturn(items);

        mockMvc.perform(get("/es/search")
                .param("q", "test")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        verify(elasticsearchSyncService, times(1)).searchItemsByKeyword("test", 0, 10);
    }

    @Test
    void testAdvancedSearch_WithNonMapResult() throws Exception {
        // 测试非Map类型的结果
        List<Map<String, Object>> items = Arrays.asList(createMockItem(1L, "测试商品"));
        when(elasticsearchSyncService.advancedSearch(eq("test"), isNull(), isNull(), isNull(),
                isNull(), isNull(), eq(0), eq(10), isNull())).thenReturn(items);

        mockMvc.perform(get("/es/search/advanced")
                .param("q", "test")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        verify(elasticsearchSyncService, times(1)).advancedSearch(eq("test"), isNull(), isNull(), isNull(),
                isNull(), isNull(), eq(0), eq(10), isNull());
    }

    @Test
    void testDirectSearch_WithNonMapResult() throws Exception {
        // 测试非Map类型的结果
        List<Map<String, Object>> items = Arrays.asList(createMockItem(1L, "测试商品"));
        when(elasticsearchSyncService.directSearchByKeyword("test", 0, 10)).thenReturn(items);

        mockMvc.perform(get("/es/direct/search")
                .param("q", "test")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        verify(elasticsearchSyncService, times(1)).directSearchByKeyword("test", 0, 10);
    }

    @Test
    void testDirectSearch_WithSourceMarker() throws Exception {
        // 测试带有source标记的结果
        Map<String, Object> result = new HashMap<>();
        result.put("source", "elasticsearch");
        result.put("items", Arrays.asList(createMockItem(1L, "测试商品")));
        result.put("total", 1);

        when(elasticsearchSyncService.directSearchByKeyword("test", 0, 10)).thenReturn(result);

        mockMvc.perform(get("/es/direct/search")
                .param("q", "test")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        verify(elasticsearchSyncService, times(1)).directSearchByKeyword("test", 0, 10);
    }

    @Test
    void testDirectSearch_WithNullResult() throws Exception {
        // 测试null结果
        when(elasticsearchSyncService.directSearchByKeyword("test", 0, 10)).thenReturn(null);

        mockMvc.perform(get("/es/direct/search")
                .param("q", "test")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        verify(elasticsearchSyncService, times(1)).directSearchByKeyword("test", 0, 10);
    }
}